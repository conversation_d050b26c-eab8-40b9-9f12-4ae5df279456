@extends('admin.layouts.master')
@section('titlePage', 'فاتورة شراء')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">فاتورة شراء</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">فاتورة شراء</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-success btn-icon me-2" onclick="printInvoice()">
                    <i class="mdi mdi-printer"></i>
                </button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-info btn-icon me-2" onclick="saveInvoice()">
                    <i class="mdi mdi-content-save"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <div class="row">
        <!-- القسم الرئيسي: بيانات الفاتورة والمنتجات -->
        <div class="col-xl-8">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        بيانات فاتورة الشراء
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="#" id="purchaseInvoiceForm">
                        @csrf
                        <div class="row">
                            <!-- رقم الفاتورة -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="invoiceNumber" name="invoice_number"
                                        value="PUR-{{ date('Y') }}-{{ str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT) }}" readonly>
                                    <label for="invoiceNumber">رقم الفاتورة</label>
                                </div>
                            </div>

                            <!-- المورد -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="supplier" name="supplier_id" required>
                                        <option value="">اختر المورد</option>
                                        <option value="1">شركة الأجهزة المتقدمة</option>
                                        <option value="2">مؤسسة التقنية الحديثة</option>
                                        <option value="3">شركة الإلكترونيات المصرية</option>
                                        <option value="4">مجموعة الحاسوب الذكي</option>
                                        <option value="5">شركة المعدات التقنية</option>
                                    </select>
                                    <label for="supplier">المورد</label>
                                </div>
                            </div>

                            <!-- تاريخ الفاتورة -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="invoiceDate" name="invoice_date"
                                        value="{{ date('Y-m-d') }}" required>
                                    <label for="invoiceDate">تاريخ الفاتورة</label>
                                </div>
                            </div>

                            <!-- نوع الدفع -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="paymentType" name="payment_type" required onchange="toggleDueDate()">
                                        <option value="">اختر نوع الدفع</option>
                                        <option value="cash">كاش</option>
                                        <option value="credit">أجل</option>
                                    </select>
                                    <label for="paymentType">نوع الدفع</label>
                                </div>
                            </div>

                            <!-- تاريخ الاستحقاق -->
                            <div class="col-md-4" id="dueDateContainer" style="display: none;">
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="dueDate" name="due_date">
                                    <label for="dueDate">تاريخ الاستحقاق</label>
                                </div>
                            </div>

                            <!-- رقم فاتورة المورد -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="supplierInvoiceNumber" name="supplier_invoice_number">
                                    <label for="supplierInvoiceNumber">رقم فاتورة المورد</label>
                                </div>
                            </div>

                            <!-- ملاحظات -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="notes" name="notes" style="height: 60px"></textarea>
                                    <label for="notes">ملاحظات</label>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- جدول المنتجات -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">المنتجات</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="addProductRow()">
                                <i class="mdi mdi-plus"></i> إضافة منتج
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered" id="productsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="20%">المخزن</th>
                                        <th width="25%">المنتج</th>
                                        <th width="12%">الكمية</th>
                                        <th width="15%">سعر الشراء</th>
                                        <th width="12%">الخصم</th>
                                        <th width="13%">الإجمالي</th>
                                        <th width="3%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- سيتم إضافة الصفوف هنا ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- القسم الجانبي: ملخص الفاتورة -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">ملخص فاتورة الشراء</div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold">المجموع الفرعي:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="subtotal" class="fs-5">0.00</span> جنيه
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold">إجمالي الخصم:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="totalDiscount" class="fs-5 text-success">0.00</span> جنيه
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label">الضريبة (14%):</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="taxAmount" class="fs-5">0.00</span> جنيه
                        </div>
                    </div>

                    <hr>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold fs-4">الإجمالي النهائي:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="grandTotal" class="fs-4 fw-bold text-primary">0.00</span> جنيه
                        </div>
                    </div>

                    <hr>

                    <!-- معلومات الدفع -->
                    <h6 class="mb-3">معلومات الدفع</h6>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label">حالة الفاتورة:</label>
                            <select class="form-select" id="invoiceStatus" name="status">
                                <option value="draft">مسودة</option>
                                <option value="pending">معلقة</option>
                                <option value="paid">مدفوعة</option>
                                <option value="cancelled">ملغية</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label">المبلغ المدفوع:</label>
                            <input type="number" class="form-control" id="paidAmount" name="paid_amount"
                                   step="0.01" min="0" onchange="calculateBalance()">
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-6">
                            <label class="form-label fw-bold">المبلغ المتبقي:</label>
                        </div>
                        <div class="col-6 text-end">
                            <div class="fw-bold text-danger fs-5" id="remainingAmount">0.00 جنيه</div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-lg" onclick="saveInvoice()">
                            <i class="mdi mdi-content-save"></i> حفظ الفاتورة
                        </button>
                        <button type="button" class="btn btn-info btn-lg" onclick="printInvoice()">
                            <i class="mdi mdi-printer"></i> طباعة الفاتورة
                        </button>
                    </div>
                </hr>
            </div>
        </div>
    </div>

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <style>
        .select2-container--bootstrap-5 .select2-selection {
            min-height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .select2-container {
            width: 100% !important;
        }

        .position-relative {
            position: relative;
        }
    </style>

    <script>
        let productRowCounter = 0;

        // إظهار/إخفاء تاريخ الاستحقاق
        function toggleDueDate() {
            const paymentType = document.getElementById('paymentType').value;
            const dueDateContainer = document.getElementById('dueDateContainer');

            if (paymentType === 'credit') {
                dueDateContainer.style.display = 'block';
                document.getElementById('dueDate').required = true;
            } else {
                dueDateContainer.style.display = 'none';
                document.getElementById('dueDate').required = false;
            }
        }

        // إضافة صف منتج جديد
        function addProductRow() {
            productRowCounter++;
            const tbody = document.getElementById('productsTableBody');
            const row = document.createElement('tr');
            row.id = `productRow${productRowCounter}`;

            row.innerHTML = `
                <td>
                    <select class="form-select form-select-sm" name="products[${productRowCounter}][warehouse_id]" required>
                        <option value="">اختر المخزن</option>
                        <option value="1">المخزن الرئيسي</option>
                        <option value="2">مخزن الفرع الأول</option>
                        <option value="3">مخزن الفرع الثاني</option>
                    </select>
                </td>
                <td>
                    <div class="position-relative">
                        <select class="form-select form-select-sm product-select"
                                name="products[${productRowCounter}][product_id]"
                                id="productSelect${productRowCounter}"
                                required>
                            <option value="">ابحث واختر المنتج...</option>
                        </select>

                        <input type="text"
                               class="form-control form-control-sm mt-2"
                               name="products[${productRowCounter}][product_name]"
                               id="productInput${productRowCounter}"
                               placeholder="اسم المنتج الجديد"
                               style="display: none;">
                    </div>
                    <small class="text-muted" id="stockInfo${productRowCounter}"></small>
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" name="products[${productRowCounter}][quantity]"
                           min="1" value="1" required onchange="calculateRowTotal(${productRowCounter})">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" name="products[${productRowCounter}][purchase_price]"
                           step="0.01" min="0" required onchange="calculateRowTotal(${productRowCounter})">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" name="products[${productRowCounter}][discount]"
                           step="0.01" min="0" value="0" onchange="calculateRowTotal(${productRowCounter})">
                </td>
                <td>
                    <span class="fw-bold" id="rowTotal${productRowCounter}">0.00</span>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeProductRow(${productRowCounter})">
                        <i class="mdi mdi-delete"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);

            // تعبئة قائمة المنتجات للصف الجديد
            populateProductSelect(productRowCounter);
        }

        // قائمة المنتجات الموجودة مع الكميات
        const existingProducts = [
            {id: 1, name: 'لابتوب HP', price: 12000, stock: 15, warehouse_id: '1'},
            {id: 2, name: 'ماوس لاسلكي', price: 200, stock: 100, warehouse_id: '1'},
            {id: 3, name: 'كيبورد ميكانيكي', price: 600, stock: 50, warehouse_id: '1'},
            {id: 4, name: 'شاشة 24 بوصة', price: 3200, stock: 25, warehouse_id: '1'},
            {id: 5, name: 'سماعات بلوتوث', price: 800, stock: 30, warehouse_id: '1'},
            {id: 6, name: 'طابعة ليزر', price: 2400, stock: 10, warehouse_id: '2'},
            {id: 7, name: 'كاميرا ويب', price: 480, stock: 20, warehouse_id: '2'},
            {id: 8, name: 'هارد خارجي 1TB', price: 1200, stock: 15, warehouse_id: '2'},
            {id: 9, name: 'راوتر واي فاي', price: 720, stock: 12, warehouse_id: '2'},
            {id: 10, name: 'تابلت سامسونج', price: 4800, stock: 8, warehouse_id: '3'},
            {id: 11, name: 'شاحن لاسلكي', price: 320, stock: 25, warehouse_id: '3'},
            {id: 12, name: 'كابل USB-C', price: 100, stock: 100, warehouse_id: '3'},
            {id: 13, name: 'باور بانك', price: 600, stock: 40, warehouse_id: '3'}
        ];

        // تحديث قائمة المنتجات عند إضافة صف جديد
        function populateProductSelect(rowId) {
            const productSelect = document.getElementById(`productSelect${rowId}`);

            // مسح الخيارات الحالية
            productSelect.innerHTML = '<option value="">ابحث واختر المنتج...</option>';

            // إضافة المنتجات
            existingProducts.forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.dataset.price = product.price;
                option.dataset.stock = product.stock;
                option.dataset.warehouseId = product.warehouse_id;
                option.textContent = `${product.name} (متوفر: ${product.stock})`;
                productSelect.appendChild(option);
            });

            // إضافة خيار المنتج الجديد
            const newOption = document.createElement('option');
            newOption.value = 'new';
            newOption.textContent = '+ إضافة منتج جديد';
            productSelect.appendChild(newOption);

            // تفعيل Select2
            initializeSelect2(rowId);
        }

        // تفعيل Select2 للصف
        function initializeSelect2(rowId) {
            const selectElement = $(`#productSelect${rowId}`);

            selectElement.select2({
                theme: 'bootstrap-5',
                placeholder: 'ابحث واختر المنتج...',
                allowClear: true,
                language: {
                    noResults: function() {
                        return 'لا توجد نتائج';
                    },
                    searching: function() {
                        return 'جاري البحث...';
                    }
                },
                templateResult: function(option) {
                    if (!option.id) return option.text;

                    if (option.id === 'new') {
                        return $('<span><i class="mdi mdi-plus text-primary"></i> ' + option.text + '</span>');
                    }

                    const product = existingProducts.find(p => p.id == option.id);
                    if (product) {
                        let stockClass = 'text-success';
                        if (product.stock < 10) stockClass = 'text-danger';
                        else if (product.stock < 20) stockClass = 'text-warning';

                        return $(`
                            <div>
                                <strong>${product.name}</strong>
                                <br>
                                <small class="${stockClass}">متوفر: ${product.stock} قطعة</small>
                                <small class="text-muted"> - سعر الشراء: ${product.price} جنيه</small>
                            </div>
                        `);
                    }

                    return option.text;
                },
                templateSelection: function(option) {
                    if (option.id === 'new') {
                        return '+ إضافة منتج جديد';
                    }
                    return option.text;
                }
            });

            // معالج تغيير الاختيار
            selectElement.on('change', function() {
                updateProductInfo(rowId);
            });
        }

        // تحديث معلومات المنتج عند الاختيار
        function updateProductInfo(rowId) {
            const productSelect = document.getElementById(`productSelect${rowId}`);
            const productInput = document.getElementById(`productInput${rowId}`);
            const stockInfo = document.getElementById(`stockInfo${rowId}`);
            const priceInput = document.querySelector(`#productRow${rowId} input[name*="[purchase_price]"]`);
            const warehouseSelect = document.querySelector(`#productRow${rowId} select[name*="[warehouse_id]"]`);
            const selectedOption = productSelect.options[productSelect.selectedIndex];

            if (productSelect.value === 'new') {
                // إظهار حقل المنتج الجديد
                productInput.style.display = 'block';
                productInput.required = true;
                stockInfo.textContent = 'منتج جديد - سيتم إضافته للمخزون';
                stockInfo.className = 'text-primary';
                priceInput.value = '';
            } else if (productSelect.value && selectedOption.dataset.price) {
                // منتج موجود
                productInput.style.display = 'none';
                productInput.required = false;

                const stock = selectedOption.dataset.stock;
                const price = selectedOption.dataset.price;
                const warehouseId = selectedOption.dataset.warehouseId;

                // تحديث السعر
                priceInput.value = price;

                // تحديث المخزن
                warehouseSelect.value = warehouseId;

                // تحديث معلومات المخزون
                stockInfo.textContent = `متوفر: ${stock} قطعة - سعر الشراء المقترح: ${price} جنيه`;

                if (stock < 10) {
                    stockInfo.className = 'text-danger';
                } else if (stock < 20) {
                    stockInfo.className = 'text-warning';
                } else {
                    stockInfo.className = 'text-success';
                }

                calculateRowTotal(rowId);
            } else {
                // لا يوجد اختيار
                productInput.style.display = 'none';
                productInput.required = false;
                stockInfo.textContent = '';
                priceInput.value = '';
            }
        }





        // حساب إجمالي الصف
        function calculateRowTotal(rowId) {
            const row = document.getElementById(`productRow${rowId}`);
            const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
            const price = parseFloat(row.querySelector('input[name*="[purchase_price]"]').value) || 0;
            const discount = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

            const total = (quantity * price) - discount;
            document.getElementById(`rowTotal${rowId}`).textContent = total.toFixed(2);

            calculateInvoiceTotal();
        }

        // حذف صف المنتج
        function removeProductRow(rowId) {
            document.getElementById(`productRow${rowId}`).remove();
            calculateInvoiceTotal();
        }

        // حساب إجمالي الفاتورة
        function calculateInvoiceTotal() {
            let subtotal = 0;
            let totalDiscount = 0;

            document.querySelectorAll('#productsTableBody tr').forEach(row => {
                const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
                const price = parseFloat(row.querySelector('input[name*="[purchase_price]"]').value) || 0;
                const discount = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

                subtotal += quantity * price;
                totalDiscount += discount;
            });

            const taxAmount = (subtotal - totalDiscount) * 0.14; // ضريبة 14% في مصر
            const grandTotal = subtotal - totalDiscount + taxAmount;

            document.getElementById('subtotal').textContent = subtotal.toFixed(2);
            document.getElementById('totalDiscount').textContent = totalDiscount.toFixed(2);
            document.getElementById('taxAmount').textContent = taxAmount.toFixed(2);
            document.getElementById('grandTotal').textContent = grandTotal.toFixed(2);

            calculateBalance();
        }

        // حساب المبلغ المتبقي
        function calculateBalance() {
            const grandTotal = parseFloat(document.getElementById('grandTotal').textContent) || 0;
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
            const remaining = grandTotal - paidAmount;

            document.getElementById('remainingAmount').textContent = remaining.toFixed(2) + ' جنيه';
            document.getElementById('remainingAmount').className = remaining > 0 ?
                'form-control-plaintext fw-bold text-danger' :
                'form-control-plaintext fw-bold text-success';
        }

        // حفظ الفاتورة
        function saveInvoice() {
            const form = document.getElementById('purchaseInvoiceForm');
            if (form.checkValidity()) {
                alert('تم حفظ فاتورة الشراء بنجاح!');
            } else {
                alert('يرجى ملء جميع الحقول المطلوبة');
                form.reportValidity();
            }
        }

        // طباعة الفاتورة
        function printInvoice() {
            window.print();
        }

        // إضافة صف منتج افتراضي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addProductRow();
        });
    </script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

@endsection
