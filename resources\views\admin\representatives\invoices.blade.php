@extends('admin.layouts.master')
@section('titlePage', 'فواتير المناديب')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">فواتير المناديب</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.representatives.index') }}">المناديب</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الفواتير</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-primary btn-icon me-2" onclick="createRepInvoice()">
                    <i class="mdi mdi-plus"></i>
                </button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-success btn-icon me-2" onclick="exportInvoices()">
                    <i class="mdi mdi-file-excel"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- فلاتر البحث -->
    <div class="row mb-3">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">فلاتر البحث</div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="representativeFilter">
                                    <option value="">جميع المناديب</option>
                                    <option value="1">أحمد سالم المندوب</option>
                                    <option value="2">محمد عبدالرحمن</option>
                                    <option value="3">سارة أحمد المصري</option>
                                </select>
                                <label for="representativeFilter">المندوب</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">معلقة</option>
                                    <option value="approved">موافق عليها</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="rejected">مرفوضة</option>
                                </select>
                                <label for="statusFilter">الحالة</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="date" class="form-control" id="dateFrom">
                                <label for="dateFrom">من تاريخ</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="date" class="form-control" id="dateTo">
                                <label for="dateTo">إلى تاريخ</label>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" onclick="filterInvoices()">
                            <i class="mdi mdi-magnify"></i> بحث
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearFilters()">
                            <i class="mdi mdi-refresh"></i> مسح
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الفواتير -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="card-title">فواتير المناديب</div>
                    <div>
                        <span class="badge bg-primary" id="invoicesCount">12 فاتورة</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="invoicesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th width="8%">رقم الفاتورة</th>
                                    <th width="15%">المندوب</th>
                                    <th width="10%">التاريخ</th>
                                    <th width="12%">نوع الفاتورة</th>
                                    <th width="12%">المبلغ</th>
                                    <th width="10%">العمولة</th>
                                    <th width="10%">نسبة العمولة</th>
                                    <th width="8%">الحالة</th>
                                    <th width="15%">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="invoicesTableBody">
                                <tr>
                                    <td><span class="badge bg-primary">REP-2024-0001</span></td>
                                    <td class="fw-bold">أحمد سالم المندوب</td>
                                    <td>2024-01-15</td>
                                    <td><span class="badge bg-success">عمولة مبيعات</span></td>
                                    <td class="text-success fw-bold">15,000.00 جنيه</td>
                                    <td class="text-warning fw-bold">750.00 جنيه</td>
                                    <td class="text-primary">5%</td>
                                    <td><span class="badge bg-success">مدفوعة</span></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-info btn-sm" onclick="viewInvoice('REP-2024-0001')" title="عرض">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            <button class="btn btn-warning btn-sm" onclick="editInvoice('REP-2024-0001')" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button class="btn btn-success btn-sm" onclick="printInvoice('REP-2024-0001')" title="طباعة">
                                                <i class="mdi mdi-printer"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">REP-2024-0002</span></td>
                                    <td class="fw-bold">محمد عبدالرحمن</td>
                                    <td>2024-01-18</td>
                                    <td><span class="badge bg-info">عمولة خدمات</span></td>
                                    <td class="text-success fw-bold">8,500.00 جنيه</td>
                                    <td class="text-warning fw-bold">340.00 جنيه</td>
                                    <td class="text-primary">4%</td>
                                    <td><span class="badge bg-warning">معلقة</span></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-info btn-sm" onclick="viewInvoice('REP-2024-0002')" title="عرض">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            <button class="btn btn-warning btn-sm" onclick="editInvoice('REP-2024-0002')" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button class="btn btn-primary btn-sm" onclick="approveInvoice('REP-2024-0002')" title="موافقة">
                                                <i class="mdi mdi-check"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">REP-2024-0003</span></td>
                                    <td class="fw-bold">سارة أحمد المصري</td>
                                    <td>2024-01-22</td>
                                    <td><span class="badge bg-success">عمولة مبيعات</span></td>
                                    <td class="text-success fw-bold">12,300.00 جنيه</td>
                                    <td class="text-warning fw-bold">738.00 جنيه</td>
                                    <td class="text-primary">6%</td>
                                    <td><span class="badge bg-primary">موافق عليها</span></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-info btn-sm" onclick="viewInvoice('REP-2024-0003')" title="عرض">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            <button class="btn btn-warning btn-sm" onclick="editInvoice('REP-2024-0003')" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button class="btn btn-success btn-sm" onclick="payInvoice('REP-2024-0003')" title="دفع">
                                                <i class="mdi mdi-cash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إنشاء فاتورة مندوب -->
    <div class="modal fade" id="createRepInvoiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء فاتورة مندوب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="repInvoiceForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="repSelect" required>
                                        <option value="">اختر المندوب</option>
                                        <option value="1">أحمد سالم المندوب</option>
                                        <option value="2">محمد عبدالرحمن</option>
                                        <option value="3">سارة أحمد المصري</option>
                                    </select>
                                    <label for="repSelect">المندوب</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="invoiceTypeSelect" required>
                                        <option value="">اختر نوع الفاتورة</option>
                                        <option value="sales_commission">عمولة مبيعات</option>
                                        <option value="service_commission">عمولة خدمات</option>
                                        <option value="bonus">مكافأة</option>
                                        <option value="advance">سلفة</option>
                                    </select>
                                    <label for="invoiceTypeSelect">نوع الفاتورة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="invoiceDateInput" required>
                                    <label for="invoiceDateInput">تاريخ الفاتورة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" id="amountInput" step="0.01" min="0" required>
                                    <label for="amountInput">المبلغ (جنيه)</label>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="notesInput" style="height: 80px"></textarea>
                                    <label for="notesInput">ملاحظات</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveRepInvoice()">حفظ الفاتورة</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // إنشاء فاتورة مندوب جديدة
        function createRepInvoice() {
            document.getElementById('invoiceDateInput').value = new Date().toISOString().split('T')[0];
            const modal = new bootstrap.Modal(document.getElementById('createRepInvoiceModal'));
            modal.show();
        }

        // حفظ فاتورة المندوب
        function saveRepInvoice() {
            const form = document.getElementById('repInvoiceForm');
            if (form.checkValidity()) {
                alert('تم حفظ فاتورة المندوب بنجاح!');
                bootstrap.Modal.getInstance(document.getElementById('createRepInvoiceModal')).hide();
                // هنا يمكن إضافة كود الحفظ الفعلي
            } else {
                form.reportValidity();
            }
        }

        // عرض فاتورة
        function viewInvoice(invoiceId) {
            alert(`عرض تفاصيل الفاتورة: ${invoiceId}`);
        }

        // تعديل فاتورة
        function editInvoice(invoiceId) {
            alert(`تعديل الفاتورة: ${invoiceId}`);
        }

        // طباعة فاتورة
        function printInvoice(invoiceId) {
            alert(`طباعة الفاتورة: ${invoiceId}`);
        }

        // موافقة على فاتورة
        function approveInvoice(invoiceId) {
            if (confirm(`هل أنت متأكد من الموافقة على الفاتورة ${invoiceId}؟`)) {
                alert(`تم الموافقة على الفاتورة: ${invoiceId}`);
            }
        }

        // دفع فاتورة
        function payInvoice(invoiceId) {
            if (confirm(`هل أنت متأكد من دفع الفاتورة ${invoiceId}؟`)) {
                alert(`تم دفع الفاتورة: ${invoiceId}`);
            }
        }

        // فلترة الفواتير
        function filterInvoices() {
            alert('سيتم تطبيق الفلاتر');
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('representativeFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
        }

        // تصدير الفواتير
        function exportInvoices() {
            alert('سيتم تصدير فواتير المناديب إلى Excel');
        }
    </script>

@endsection
