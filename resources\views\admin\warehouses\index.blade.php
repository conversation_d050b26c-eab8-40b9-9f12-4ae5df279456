@extends('admin.layouts.master')
@section('titlePage', 'إدارة المخازن')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">إدارة المخازن</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">المخازن</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-primary btn-icon me-2" onclick="showAddWarehouseModal()">
                    <i class="mdi mdi-plus"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- إضافة مخزن جديد -->
    <div class="row mb-4">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        إضافة مخزن جديد
                    </div>
                </div>
                <div class="card-body">
                    <form id="warehouseForm">
                        <div class="row d-flex align-items-center justify-content-center">
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="warehouseName" name="name" placeholder="اسم المخزن" required>
                                    <label for="warehouseName">اسم المخزن</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="warehouseAddress" name="address" placeholder="عنوان المخزن" required>
                                    <label for="warehouseAddress">عنوان المخزن</label>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <button type="submit" class="btn btn-primary btn-block w-100">
                                    <i class="mdi mdi-plus"></i> إضافة المخزن
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المخازن -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">قائمة المخازن</div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered text-nowrap w-100 table-striped" id="warehousesTable">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="25%">اسم المخزن</th>
                                    <th width="40%">العنوان</th>
                                    <th width="15%">عدد المنتجات</th>
                                    <th width="15%">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="warehousesTableBody">
                                <tr>
                                    <td>1</td>
                                    <td class="fw-bold">
                                        <i class="mdi mdi-home-variant text-primary me-2"></i>
                                        المخزن الرئيسي
                                    </td>
                                    <td>شارع الجمهورية، وسط البلد، القاهرة</td>
                                    <td>
                                        <span class="badge bg-success">150 منتج</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-info btn-sm" onclick="viewWarehouse(1)" title="عرض">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm" onclick="editWarehouse(1)" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteWarehouse(1)" title="حذف">
                                                <i class="mdi mdi-delete"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td class="fw-bold">
                                        <i class="mdi mdi-store text-success me-2"></i>
                                        مخزن الفرع الأول
                                    </td>
                                    <td>شارع النصر، مدينة نصر، القاهرة</td>
                                    <td>
                                        <span class="badge bg-warning">85 منتج</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-info btn-sm" onclick="viewWarehouse(2)" title="عرض">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm" onclick="editWarehouse(2)" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteWarehouse(2)" title="حذف">
                                                <i class="mdi mdi-delete"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td class="fw-bold">
                                        <i class="mdi mdi-store text-warning me-2"></i>
                                        مخزن الفرع الثاني
                                    </td>
                                    <td>شارع الهرم، الجيزة</td>
                                    <td>
                                        <span class="badge bg-info">120 منتج</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-info btn-sm" onclick="viewWarehouse(3)" title="عرض">
                                                <i class="mdi mdi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm" onclick="editWarehouse(3)" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteWarehouse(3)" title="حذف">
                                                <i class="mdi mdi-delete"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تعديل المخزن -->
    <div class="modal fade" id="editWarehouseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المخزن</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editWarehouseForm">
                        <input type="hidden" id="editWarehouseId">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="editWarehouseName" required>
                            <label for="editWarehouseName">اسم المخزن</label>
                        </div>
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="editWarehouseAddress" required>
                            <label for="editWarehouseAddress">عنوان المخزن</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="updateWarehouse()">حفظ التعديلات</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let warehouseCounter = 3;

        // إضافة مخزن جديد
        document.getElementById('warehouseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('warehouseName').value;
            const address = document.getElementById('warehouseAddress').value;
            
            if (name && address) {
                warehouseCounter++;
                addWarehouseToTable(warehouseCounter, name, address);
                
                // مسح النموذج
                this.reset();
                
                alert('تم إضافة المخزن بنجاح!');
            }
        });

        // إضافة مخزن للجدول
        function addWarehouseToTable(id, name, address) {
            const tableBody = document.getElementById('warehousesTableBody');
            const newRow = document.createElement('tr');
            
            newRow.innerHTML = `
                <td>${id}</td>
                <td class="fw-bold">
                    <i class="mdi mdi-store-outline text-info me-2"></i>
                    ${name}
                </td>
                <td>${address}</td>
                <td>
                    <span class="badge bg-secondary">0 منتج</span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-info btn-sm" onclick="viewWarehouse(${id})" title="عرض">
                            <i class="mdi mdi-eye"></i>
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="editWarehouse(${id})" title="تعديل">
                            <i class="mdi mdi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteWarehouse(${id})" title="حذف">
                            <i class="mdi mdi-delete"></i>
                        </button>
                    </div>
                </td>
            `;
            
            tableBody.appendChild(newRow);
        }

        // عرض المخزن
        function viewWarehouse(id) {
            alert(`عرض تفاصيل المخزن رقم: ${id}`);
        }

        // تعديل المخزن
        function editWarehouse(id) {
            // هنا يمكن تحميل بيانات المخزن من قاعدة البيانات
            document.getElementById('editWarehouseId').value = id;
            document.getElementById('editWarehouseName').value = `مخزن رقم ${id}`;
            document.getElementById('editWarehouseAddress').value = `عنوان المخزن رقم ${id}`;
            
            const modal = new bootstrap.Modal(document.getElementById('editWarehouseModal'));
            modal.show();
        }

        // تحديث المخزن
        function updateWarehouse() {
            const id = document.getElementById('editWarehouseId').value;
            const name = document.getElementById('editWarehouseName').value;
            const address = document.getElementById('editWarehouseAddress').value;
            
            if (name && address) {
                alert(`تم تحديث المخزن رقم ${id} بنجاح!`);
                bootstrap.Modal.getInstance(document.getElementById('editWarehouseModal')).hide();
            }
        }

        // حذف المخزن
        function deleteWarehouse(id) {
            if (confirm(`هل أنت متأكد من حذف المخزن رقم ${id}؟`)) {
                alert(`تم حذف المخزن رقم ${id}`);
                // هنا يمكن إزالة الصف من الجدول
            }
        }
    </script>

@endsection
