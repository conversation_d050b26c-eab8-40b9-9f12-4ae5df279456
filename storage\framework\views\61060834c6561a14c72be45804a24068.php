<?php $__env->startSection('titlePage', 'All Warehouses'); ?>
<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1"><?php echo e(__('warehouses.all warehoueses')); ?></h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('side_bar.warehouses')); ?></li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-info btn-icon me-2 btn-b"><i
                        class="mdi mdi-filter-variant"></i></button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-danger btn-icon me-2"><i class="mdi mdi-star"></i></button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-warning  btn-icon me-2"><i class="mdi mdi-refresh"></i></button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->
    <div class="row">
        <!-- القسم الأيسر: الحقول النصية والقوائم المنسدلة -->
        <div class="col-xl-8">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <?php echo e(__('products.البيانات الاساسية')); ?>

                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="#" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <!-- اسم المنتج -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="productName" name="name"
                                        placeholder="<?php echo e(__('products.اسم المنتج')); ?>" required>
                                    <label for="productName"><?php echo e(__('products.اسم المنتج')); ?></label>
                                </div>
                            </div>

                            <!-- توزيع المنتج على المخازن -->
                            <div class="col-md-12">
                                <h6 class="mb-3 text-primary">توزيع المنتج على المخازن</h6>
                                <div class="row" id="warehousesContainer">
                                    <!-- المخزن الرئيسي -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card border warehouse-card" id="warehouseCard1">
                                            <div class="card-header bg-light py-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="warehouse1" name="warehouses[]" value="1" onchange="toggleWarehouse(1)">
                                                    <label class="form-check-label fw-bold" for="warehouse1">
                                                        <i class="mdi mdi-home-variant text-primary"></i> المخزن الرئيسي
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="card-body py-2" id="warehouse1Content" style="display: none;">
                                                <div class="form-floating mb-2">
                                                    <input type="number" class="form-control form-control-sm" id="quantity1" name="warehouse_quantities[1]" min="0" step="1" placeholder="الكمية">
                                                    <label for="quantity1">الكمية</label>
                                                </div>
                                                <small class="text-muted">
                                                    <i class="mdi mdi-information-outline"></i> الكمية المتاحة في هذا المخزن
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- مخزن الفرع الأول -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card border warehouse-card" id="warehouseCard2">
                                            <div class="card-header bg-light py-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="warehouse2" name="warehouses[]" value="2" onchange="toggleWarehouse(2)">
                                                    <label class="form-check-label fw-bold" for="warehouse2">
                                                        <i class="mdi mdi-store text-success"></i> مخزن الفرع الأول
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="card-body py-2" id="warehouse2Content" style="display: none;">
                                                <div class="form-floating mb-2">
                                                    <input type="number" class="form-control form-control-sm" id="quantity2" name="warehouse_quantities[2]" min="0" step="1" placeholder="الكمية">
                                                    <label for="quantity2">الكمية</label>
                                                </div>
                                                <small class="text-muted">
                                                    <i class="mdi mdi-information-outline"></i> الكمية المتاحة في هذا المخزن
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- مخزن الفرع الثاني -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card border warehouse-card" id="warehouseCard3">
                                            <div class="card-header bg-light py-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="warehouse3" name="warehouses[]" value="3" onchange="toggleWarehouse(3)">
                                                    <label class="form-check-label fw-bold" for="warehouse3">
                                                        <i class="mdi mdi-store text-warning"></i> مخزن الفرع الثاني
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="card-body py-2" id="warehouse3Content" style="display: none;">
                                                <div class="form-floating mb-2">
                                                    <input type="number" class="form-control form-control-sm" id="quantity3" name="warehouse_quantities[3]" min="0" step="1" placeholder="الكمية">
                                                    <label for="quantity3">الكمية</label>
                                                </div>
                                                <small class="text-muted">
                                                    <i class="mdi mdi-information-outline"></i> الكمية المتاحة في هذا المخزن
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- إضافة مخزن جديد -->
                                <div class="col-md-4">
                                    <div class="card border-dashed border-primary">
                                        <div class="card-body text-center py-4">
                                            <button type="button" class="btn btn-outline-primary" onclick="addNewWarehouse()">
                                                <i class="mdi mdi-plus-circle"></i><br>
                                                إضافة مخزن جديد
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- ملخص الكميات -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <div class="row align-items-center">
                                                <div class="col-md-3">
                                                    <strong>إجمالي الكمية:</strong><br>
                                                    <span id="totalQuantity" class="text-primary fw-bold fs-5">0</span> قطعة
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>عدد المخازن المختارة:</strong><br>
                                                    <span id="selectedWarehouses" class="text-success fw-bold fs-5">0</span> مخزن
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>متوسط الكمية لكل مخزن:</strong><br>
                                                    <span id="averageQuantity" class="text-info fw-bold fs-5">0</span> قطعة
                                                </div>
                                                <div class="col-md-3 text-center">
                                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="distributeQuantityEvenly()" title="توزيع الكمية بالتساوي">
                                                        <i class="mdi mdi-distribute-horizontal-left"></i><br>
                                                        <small>توزيع متساوي</small>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- التصنيف (قائمة منسدلة) -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="category" name="category_id" required>
                                        <option value="">اختر التصنيف</option>
                                        
                                    </select>
                                    <label for="category"><?php echo e(__('products.التصنيف')); ?></label>
                                </div>
                            </div>

                            <!-- البراند (قائمة منسدلة) -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="brand" name="brand_id" required>
                                        <option value="">اختر البراند</option>
                                        
                                    </select>
                                    <label for="brand"><?php echo e(__('products.البراند')); ?></label>
                                </div>
                            </div>



                            <!-- سعر الشراء -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" step="0.01" class="form-control" id="purchasePrice" name="purchase_price"
                                        placeholder="<?php echo e(__('products.سعر الشراء')); ?>" required>
                                    <label for="purchasePrice"><?php echo e(__('products.سعر الشراء')); ?></label>
                                </div>
                            </div>

                            <!-- وصف المنتج -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="description" name="description"
                                        placeholder="<?php echo e(__('products.وصف المنتج')); ?>" style="height: 100px"></textarea>
                                    <label for="description"><?php echo e(__('products.وصف المنتج')); ?></label>
                                </div>
                            </div>

                            <!-- كود المنتج -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="productCode" name="code"
                                        placeholder="<?php echo e(__('products.كود المنتج')); ?>" required>
                                    <label for="productCode"><?php echo e(__('products.كود المنتج')); ?></label>
                                </div>
                            </div>

                            <!-- الباركود -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="barcode" name="barcode"
                                        placeholder="<?php echo e(__('products.الباركود')); ?>">
                                    <label for="barcode"><?php echo e(__('products.الباركود')); ?></label>
                                </div>
                            </div>

                            <!-- الحد الأدنى للتنبيه -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="number" class="form-control" id="alertQuantity" name="alert_quantity"
                                        placeholder="<?php echo e(__('products.الحد الأدنى للتنبيه')); ?>" required>
                                    <label for="alertQuantity"><?php echo e(__('products.الحد الأدنى للتنبيه')); ?></label>
                                </div>
                            </div>

                            <!-- الوحدة -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="unit" name="unit" required>
                                        <option value="">اختر الوحدة</option>
                                        <option value="piece">قطعة</option>
                                        <option value="kg">كيلو</option>
                                        <option value="liter">لتر</option>
                                        <option value="box">علبة</option>
                                    </select>
                                    <label for="unit"><?php echo e(__('products.الوحدة')); ?></label>
                                </div>
                            </div>
                        </div>

                        <!-- زر الحفظ -->
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary"><?php echo e(__('buttons.submit product')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- القسم الأيمن: تحميل الصور -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <?php echo e(__('products.صور المنتج')); ?>

                    </div>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <!-- حقل تحميل الصور -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <input type="file" class="form-control" id="productImages" name="images[]" multiple>
                                    <label for="productImages"><?php echo e(__('products.صور المنتج')); ?></label>
                                </div>
                            </div>

                            <!-- معاينة الصور -->
                            <div class="col-md-12">
                                <div id="imagePreview" class="mt-3">
                                    <!-- سيتم عرض الصور المختارة هنا -->
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('productImages').addEventListener('change', function(event) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = ''; // مسح المحتوى القديم

            const files = event.target.files;
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const reader = new FileReader();

                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.width = '100px';
                    img.style.height = '100px';
                    img.style.margin = '5px';
                    preview.appendChild(img);
                };

                reader.readAsDataURL(file);
            }
        });

        // تفعيل/إلغاء تفعيل المخزن
        function toggleWarehouse(warehouseId) {
            const checkbox = document.getElementById(`warehouse${warehouseId}`);
            const content = document.getElementById(`warehouse${warehouseId}Content`);
            const quantityInput = document.getElementById(`quantity${warehouseId}`);
            const warehouseCard = document.getElementById(`warehouseCard${warehouseId}`) || checkbox.closest('.warehouse-card');

            if (checkbox.checked) {
                content.style.display = 'block';
                quantityInput.required = true;
                quantityInput.addEventListener('input', updateTotalQuantity);
                if (warehouseCard) {
                    warehouseCard.classList.add('selected');
                }
                // تركيز على حقل الكمية
                setTimeout(() => quantityInput.focus(), 100);
            } else {
                content.style.display = 'none';
                quantityInput.required = false;
                quantityInput.value = '';
                quantityInput.removeEventListener('input', updateTotalQuantity);
                if (warehouseCard) {
                    warehouseCard.classList.remove('selected');
                }
            }

            updateTotalQuantity();
            updateSelectedWarehouses();
        }

        // متغير لتتبع عدد المخازن المضافة
        let warehouseCounter = 3;

        // تحديث إجمالي الكمية
        function updateTotalQuantity() {
            let total = 0;
            let selectedCount = 0;

            // جمع الكميات من جميع المخازن المفعلة
            const checkboxes = document.querySelectorAll('input[name="warehouses[]"]:checked');
            checkboxes.forEach(checkbox => {
                const warehouseId = checkbox.value;
                const quantityInput = document.querySelector(`input[name="warehouse_quantities[${warehouseId}]"]`);

                if (quantityInput && quantityInput.value) {
                    total += parseInt(quantityInput.value) || 0;
                    selectedCount++;
                }
            });

            document.getElementById('totalQuantity').textContent = total;

            // حساب المتوسط
            const average = selectedCount > 0 ? Math.round(total / selectedCount) : 0;
            document.getElementById('averageQuantity').textContent = average;

            // تغيير لون الإجمالي حسب الكمية
            const totalElement = document.getElementById('totalQuantity');
            if (total === 0) {
                totalElement.className = 'text-muted fw-bold';
            } else if (total < 10) {
                totalElement.className = 'text-danger fw-bold';
            } else if (total < 50) {
                totalElement.className = 'text-warning fw-bold';
            } else {
                totalElement.className = 'text-success fw-bold';
            }
        }

        // تحديث عدد المخازن المختارة
        function updateSelectedWarehouses() {
            const selectedCount = document.querySelectorAll('input[name="warehouses[]"]:checked').length;
            document.getElementById('selectedWarehouses').textContent = selectedCount;

            // تغيير لون العدد
            const selectedElement = document.getElementById('selectedWarehouses');
            if (selectedCount === 0) {
                selectedElement.className = 'text-muted fw-bold';
            } else {
                selectedElement.className = 'text-success fw-bold';
            }
        }

        // إضافة مخزن جديد
        function addNewWarehouse() {
            const warehouseName = prompt('أدخل اسم المخزن الجديد:');
            if (!warehouseName || warehouseName.trim() === '') {
                return;
            }

            warehouseCounter++;
            const warehouseId = warehouseCounter;

            const warehousesContainer = document.getElementById('warehousesContainer');
            const addButton = warehousesContainer.querySelector('.card.border-dashed');

            // إنشاء المخزن الجديد
            const newWarehouseDiv = document.createElement('div');
            newWarehouseDiv.className = 'col-md-4';
            newWarehouseDiv.innerHTML = `
                <div class="card border">
                    <div class="card-header bg-light py-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="warehouse${warehouseId}" name="warehouses[]" value="${warehouseId}" onchange="toggleWarehouse(${warehouseId})">
                            <label class="form-check-label fw-bold" for="warehouse${warehouseId}">
                                ${warehouseName.trim()}
                            </label>
                            <button type="button" class="btn btn-sm btn-outline-danger float-end" onclick="removeWarehouse(${warehouseId})" title="حذف المخزن">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body py-2" id="warehouse${warehouseId}Content" style="display: none;">
                        <div class="form-floating">
                            <input type="number" class="form-control form-control-sm" id="quantity${warehouseId}" name="warehouse_quantities[${warehouseId}]" min="0" step="1" placeholder="الكمية">
                            <label for="quantity${warehouseId}">الكمية</label>
                        </div>
                        <small class="text-muted">الكمية المتاحة في هذا المخزن</small>
                    </div>
                </div>
            `;

            // إدراج المخزن الجديد قبل زر الإضافة
            addButton.parentNode.parentNode.insertBefore(newWarehouseDiv, addButton.parentNode);

            // إضافة مستمع الحدث للكمية الجديدة
            const quantityInput = document.getElementById(`quantity${warehouseId}`);
            quantityInput.addEventListener('input', updateTotalQuantity);
        }

        // حذف مخزن
        function removeWarehouse(warehouseId) {
            if (confirm('هل أنت متأكد من حذف هذا المخزن؟')) {
                const warehouseElement = document.getElementById(`warehouse${warehouseId}`).closest('.col-md-4');
                warehouseElement.remove();
                updateTotalQuantity();
                updateSelectedWarehouses();
            }
        }

        // إضافة مستمعي الأحداث للكميات
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة مستمعي الأحداث للمخازن الأساسية
            for (let i = 1; i <= 3; i++) {
                const quantityInput = document.getElementById(`quantity${i}`);
                if (quantityInput) {
                    quantityInput.addEventListener('input', updateTotalQuantity);
                }
            }

            // تحديث الملخص الأولي
            updateTotalQuantity();
            updateSelectedWarehouses();
        });

        // التحقق من صحة النموذج قبل الإرسال
        document.getElementById('productForm').addEventListener('submit', function(e) {
            const selectedWarehouses = parseInt(document.getElementById('selectedWarehouses').textContent);
            const totalQuantity = parseInt(document.getElementById('totalQuantity').textContent);

            if (selectedWarehouses === 0) {
                e.preventDefault();
                alert('يجب اختيار مخزن واحد على الأقل');
                return false;
            }

            if (totalQuantity === 0) {
                e.preventDefault();
                alert('يجب إدخال كمية أكبر من صفر');
                return false;
            }

            // التحقق من أن كل مخزن مختار له كمية
            const checkedWarehouses = document.querySelectorAll('input[name="warehouses[]"]:checked');
            let hasEmptyQuantity = false;

            checkedWarehouses.forEach(checkbox => {
                const warehouseId = checkbox.value;
                const quantityInput = document.querySelector(`input[name="warehouse_quantities[${warehouseId}]"]`);

                if (!quantityInput.value || parseInt(quantityInput.value) <= 0) {
                    hasEmptyQuantity = true;
                }
            });

            if (hasEmptyQuantity) {
                e.preventDefault();
                alert('يجب إدخال كمية صحيحة لجميع المخازن المختارة');
                return false;
            }

            return true;
        });

        // دالة لتوزيع الكمية بالتساوي على المخازن المختارة
        function distributeQuantityEvenly() {
            const totalQuantityInput = prompt('أدخل إجمالي الكمية المراد توزيعها:');
            if (!totalQuantityInput || isNaN(totalQuantityInput) || parseInt(totalQuantityInput) <= 0) {
                return;
            }

            const totalQuantity = parseInt(totalQuantityInput);
            const checkedWarehouses = document.querySelectorAll('input[name="warehouses[]"]:checked');

            if (checkedWarehouses.length === 0) {
                alert('يجب اختيار مخزن واحد على الأقل أولاً');
                return;
            }

            const quantityPerWarehouse = Math.floor(totalQuantity / checkedWarehouses.length);
            const remainder = totalQuantity % checkedWarehouses.length;

            checkedWarehouses.forEach((checkbox, index) => {
                const warehouseId = checkbox.value;
                const quantityInput = document.querySelector(`input[name="warehouse_quantities[${warehouseId}]"]`);

                let quantity = quantityPerWarehouse;
                // إضافة الباقي للمخازن الأولى
                if (index < remainder) {
                    quantity += 1;
                }

                quantityInput.value = quantity;
            });

            updateTotalQuantity();
        }
    </script>

    <style>
        .warehouse-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .warehouse-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .warehouse-card.selected {
            border-color: #007bff !important;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .warehouse-card .card-header {
            border-bottom: 2px solid #dee2e6;
        }

        .warehouse-card.selected .card-header {
            background-color: #e7f3ff !important;
            border-bottom-color: #007bff;
        }

        .form-floating > .form-control-sm {
            height: calc(2.5rem + 2px);
            padding: 0.75rem 0.75rem;
        }

        .form-floating > .form-control-sm ~ label {
            padding: 0.75rem 0.75rem;
        }

        .border-dashed {
            border-style: dashed !important;
            border-width: 2px !important;
        }

        .border-dashed:hover {
            border-color: #007bff !important;
            background-color: #f8f9fa;
        }

        #totalQuantity.text-danger {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/admin/products/create.blade.php ENDPATH**/ ?>