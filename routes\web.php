<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/



// routes/web.php
Auth::routes();

Route::group(
    [
        'prefix' => LaravelLocalization::setLocale(),
        'middleware' => [ 'localeSessionRedirect', 'localizationRedirect', 'localeViewPath' ]
    ], function () {

        Route::get('/', function () {return view('website.index');})->name('home');

    /** ADD ALL LOCALIZED ROUTES INSIDE ADMIN ROUTS **/
    Route::prefix('dashboard')->middleware(['auth', 'admin'])->group(function () {
        // Admin Dashboard
        Route::get('/', function () {return view('admin.index');})->name('admin.dashboard');

        // Wateourses Admin Dashboard
        Route::get('/warehouses', function () {return view('admin.warehouses.create');})->name('admin.warehouses');
        Route::get('/categories', function () {return view('admin.categories.create');})->name('admin.categories');
        Route::get('/products/create', function () {return view('admin.products.create');})->name('admin.products.create');
        Route::get('/products', function () {return view('admin.products.index');})->name('admin.products');
        Route::get('/invoice', function () {return view('admin.invoice.create');})->name('admin.invoice');
        Route::get('/invoice/sale', function () {return view('admin.invoice.sale');})->name('admin.invoice.sale');
        Route::get('/invoice/purchase', function () {return view('admin.invoice.purchase');})->name('admin.invoice.purchase');

        // Temporary routes for customers and suppliers (static pages)
        Route::get('/customers', function () {return view('admin.customers.index');})->name('admin.customers.index');
        Route::get('/customers/create', function () {return view('admin.customers.create');})->name('admin.customers.create');
        Route::get('/customers/edit', function () {return view('admin.customers.edit');})->name('admin.customers.edit');
        Route::get('/customers/show', function () {return view('admin.customers.show');})->name('admin.customers.show');

        Route::get('/suppliers', function () {return view('admin.suppliers.index');})->name('admin.suppliers.index');
        Route::get('/suppliers/create', function () {return view('admin.suppliers.create');})->name('admin.suppliers.create');
        Route::get('/suppliers/edit', function () {return view('admin.suppliers.edit');})->name('admin.suppliers.edit');
        Route::get('/suppliers/show', function () {return view('admin.suppliers.show');})->name('admin.suppliers.show');
    });

});


/** OTHER PAGES THAT SHOULD NOT BE LOCALIZED **/

