@extends('admin.layouts.master')
@section('titlePage', 'الموردين')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">الموردين</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الموردين</li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <a href="#" class="btn btn-primary">
                    <i class="mdi mdi-plus"></i> إضافة مورد جديد
                </a>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">قائمة الموردين</div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="15%">الاسم</th>
                                    <th width="15%">البريد الإلكتروني</th>
                                    <th width="10%">الهاتف</th>
                                    <th width="10%">المدينة</th>
                                    <th width="10%">الشخص المسؤول</th>
                                    <th width="10%">لنا (مدين)</th>
                                    <th width="10%">علينا (دائن)</th>
                                    <th width="8%">آخر استحقاق</th>
                                    <th width="5%">الحالة</th>
                                    <th width="12%">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td class="fw-bold">شركة الأجهزة المتقدمة</td>
                                    <td><EMAIL></td>
                                    <td>0112345678</td>
                                    <td>القاهرة</td>
                                    <td>خالد العتيبي</td>
                                    <td class="text-success fw-bold">15,000.00 جنيه</td>
                                    <td class="text-danger fw-bold">42,500.00 جنيه</td>
                                    <td>
                                        <span class="text-warning">2024-02-20</span>
                                        <br><small class="text-muted">بعد 10 أيام</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">نشط</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                <i class="mdi mdi-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="showSupplierFinancialDetails(1)" title="الحساب المالي">
                                                <i class="mdi mdi-cash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>مؤسسة التقنية الحديثة</td>
                                    <td><EMAIL></td>
                                    <td>0126789012</td>
                                    <td>جدة</td>
                                    <td>سارة أحمد</td>
                                    <td>-10,000.00</td>
                                    <td>
                                        <span class="badge bg-success">نشط</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm">
                                                <i class="mdi mdi-eye"></i> عرض
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm">
                                                <i class="mdi mdi-pencil"></i> تعديل
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا المورد؟')">
                                                <i class="mdi mdi-delete"></i> حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>شركة المواد الخام المحدودة</td>
                                    <td><EMAIL></td>
                                    <td>0138901234</td>
                                    <td>الدمام</td>
                                    <td>عبدالله الدوسري</td>
                                    <td>-50,000.00</td>
                                    <td>
                                        <span class="badge bg-success">نشط</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm">
                                                <i class="mdi mdi-eye"></i> عرض
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm">
                                                <i class="mdi mdi-pencil"></i> تعديل
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا المورد؟')">
                                                <i class="mdi mdi-delete"></i> حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Modal التفاصيل المالية للموردين -->
    <div class="modal fade" id="supplierFinancialModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">التفاصيل المالية للمورد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="supplierFinancialContent">
                    <!-- سيتم عرض التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary">طباعة كشف الحساب</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الموردين المالية
        const suppliersFinancialData = {
            1: {
                name: 'شركة الأجهزة المتقدمة',
                debit: 15000,  // لنا (دفعنا لهم)
                credit: 42500, // علينا (مستحق لهم)
                transactions: [
                    {date: '2024-01-10', type: 'purchase', amount: 35000, description: 'فاتورة شراء رقم PUR-2024-0001', due_date: '2024-02-20'},
                    {date: '2024-01-15', type: 'payment', amount: -20000, description: 'دفعة نقدية', due_date: null},
                    {date: '2024-01-25', type: 'purchase', amount: 27500, description: 'فاتورة شراء رقم PUR-2024-0008', due_date: '2024-02-25'},
                    {date: '2024-02-01', type: 'payment', amount: -15000, description: 'دفعة نقدية', due_date: null},
                    {date: '2024-02-05', type: 'return', amount: -15000, description: 'مرتجع بضاعة', due_date: null}
                ]
            }
        };

        // عرض التفاصيل المالية للمورد
        function showSupplierFinancialDetails(supplierId) {
            const supplier = suppliersFinancialData[supplierId];
            if (!supplier) return;

            const modalContent = document.getElementById('supplierFinancialContent');

            // حساب الرصيد النهائي (علينا - لنا)
            const balance = supplier.credit - supplier.debit;
            const balanceClass = balance >= 0 ? 'text-danger' : 'text-success';
            const balanceText = balance >= 0 ? 'علينا' : 'لنا';

            modalContent.innerHTML = `
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary">ملخص الحساب</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5>${supplier.debit.toLocaleString()} جنيه</h5>
                                        <small>إجمالي لنا (دفعنا)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h5>${supplier.credit.toLocaleString()} جنيه</h5>
                                        <small>إجمالي علينا (مستحق)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5>${Math.abs(balance).toLocaleString()} جنيه</h5>
                                        <small>الرصيد النهائي (${balanceText})</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5>${supplier.transactions.length}</h5>
                                        <small>عدد المعاملات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary">كشف الحساب</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>البيان</th>
                                        <th>لنا</th>
                                        <th>علينا</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            supplier.transactions.forEach(transaction => {
                const isCredit = transaction.amount > 0; // علينا (مشتريات)
                const amount = Math.abs(transaction.amount);
                let typeIcon = '';

                switch(transaction.type) {
                    case 'purchase': typeIcon = 'mdi-cart-plus'; break;
                    case 'payment': typeIcon = 'mdi-cash'; break;
                    case 'return': typeIcon = 'mdi-cart-minus'; break;
                }

                let statusBadge = '';
                if (transaction.due_date) {
                    const dueDate = new Date(transaction.due_date);
                    const today = new Date();
                    const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

                    if (diffDays < 0) {
                        statusBadge = `<span class="badge bg-danger">متأخر ${Math.abs(diffDays)} يوم</span>`;
                    } else if (diffDays <= 5) {
                        statusBadge = `<span class="badge bg-warning">يستحق خلال ${diffDays} يوم</span>`;
                    } else {
                        statusBadge = `<span class="badge bg-success">يستحق بعد ${diffDays} يوم</span>`;
                    }
                } else {
                    statusBadge = '<span class="badge bg-secondary">مدفوع</span>';
                }

                modalContent.innerHTML += `
                    <tr>
                        <td>${new Date(transaction.date).toLocaleDateString('ar-EG')}</td>
                        <td><i class="mdi ${typeIcon} me-1"></i>${transaction.description}</td>
                        <td class="${!isCredit ? 'text-success fw-bold' : ''}">${!isCredit ? amount.toLocaleString() + ' جنيه' : '-'}</td>
                        <td class="${isCredit ? 'text-danger fw-bold' : ''}">${isCredit ? amount.toLocaleString() + ' جنيه' : '-'}</td>
                        <td>${transaction.due_date ? new Date(transaction.due_date).toLocaleDateString('ar-EG') : '-'}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });

            modalContent.innerHTML += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('supplierFinancialModal'));
            modal.show();
        }
    </script>

@endsection
