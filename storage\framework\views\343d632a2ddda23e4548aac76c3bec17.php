<?php $__env->startSection('titlePage', 'المناديب'); ?>
<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">إدارة المناديب</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">المناديب</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <a href="<?php echo e(route('admin.representatives.create')); ?>" class="btn btn-primary btn-icon me-2">
                    <i class="mdi mdi-plus"></i>
                </a>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-success btn-icon me-2" onclick="exportData()">
                    <i class="mdi mdi-file-excel"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">قائمة المناديب</div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="15%">الاسم</th>
                                    <th width="12%">رقم الهاتف</th>
                                    <th width="15%">البريد الإلكتروني</th>
                                    <th width="10%">المنطقة</th>
                                    <th width="10%">نسبة العمولة</th>
                                    <th width="12%">إجمالي المبيعات</th>
                                    <th width="10%">العمولة المستحقة</th>
                                    <th width="8%">الحالة</th>
                                    <th width="13%">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td class="fw-bold">أحمد سالم المندوب</td>
                                    <td>01012345678</td>
                                    <td><EMAIL></td>
                                    <td>القاهرة والجيزة</td>
                                    <td class="text-primary fw-bold">5%</td>
                                    <td class="text-success fw-bold">125,000.00 جنيه</td>
                                    <td class="text-warning fw-bold">6,250.00 جنيه</td>
                                    <td>
                                        <span class="badge bg-success">نشط</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                <i class="mdi mdi-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="showCommissionDetails(1)" title="تفاصيل العمولة">
                                                <i class="mdi mdi-cash-multiple"></i>
                                            </button>
                                            <a href="<?php echo e(route('admin.representatives.invoices')); ?>?rep_id=1" class="btn btn-secondary btn-sm" title="فواتير المندوب">
                                                <i class="mdi mdi-file-document-multiple"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td class="fw-bold">محمد عبدالرحمن</td>
                                    <td>01098765432</td>
                                    <td><EMAIL></td>
                                    <td>الإسكندرية</td>
                                    <td class="text-primary fw-bold">4%</td>
                                    <td class="text-success fw-bold">89,500.00 جنيه</td>
                                    <td class="text-warning fw-bold">3,580.00 جنيه</td>
                                    <td>
                                        <span class="badge bg-success">نشط</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                <i class="mdi mdi-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="showCommissionDetails(2)" title="تفاصيل العمولة">
                                                <i class="mdi mdi-cash-multiple"></i>
                                            </button>
                                            <a href="<?php echo e(route('admin.representatives.invoices')); ?>?rep_id=2" class="btn btn-secondary btn-sm" title="فواتير المندوب">
                                                <i class="mdi mdi-file-document-multiple"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td class="fw-bold">سارة أحمد المصري</td>
                                    <td>01155443322</td>
                                    <td><EMAIL></td>
                                    <td>المنوفية والغربية</td>
                                    <td class="text-primary fw-bold">6%</td>
                                    <td class="text-success fw-bold">67,800.00 جنيه</td>
                                    <td class="text-warning fw-bold">4,068.00 جنيه</td>
                                    <td>
                                        <span class="badge bg-warning">معلق</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                <i class="mdi mdi-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="showCommissionDetails(3)" title="تفاصيل العمولة">
                                                <i class="mdi mdi-cash-multiple"></i>
                                            </button>
                                            <a href="<?php echo e(route('admin.representatives.invoices')); ?>?rep_id=3" class="btn btn-secondary btn-sm" title="فواتير المندوب">
                                                <i class="mdi mdi-file-document-multiple"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل العمولة -->
    <div class="modal fade" id="commissionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل عمولة المندوب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="commissionContent">
                    <!-- سيتم عرض التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-success">صرف العمولة</button>
                    <button type="button" class="btn btn-primary">طباعة التقرير</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المناديب والعمولات
        const representativesData = {
            1: {
                name: 'أحمد سالم المندوب',
                commission_rate: 5,
                total_sales: 125000,
                total_commission: 6250,
                sales: [
                    {date: '2024-01-15', invoice: 'SALE-2024-0001', customer: 'أحمد محمد علي', amount: 15000, commission: 750},
                    {date: '2024-01-20', invoice: 'SALE-2024-0008', customer: 'فاطمة أحمد', amount: 22000, commission: 1100},
                    {date: '2024-01-25', invoice: 'SALE-2024-0015', customer: 'محمد عبدالله', amount: 18500, commission: 925},
                    {date: '2024-02-01', invoice: 'SALE-2024-0022', customer: 'سارة حسن', amount: 25000, commission: 1250},
                    {date: '2024-02-05', invoice: 'SALE-2024-0028', customer: 'علي محمود', amount: 44500, commission: 2225}
                ]
            },
            2: {
                name: 'محمد عبدالرحمن',
                commission_rate: 4,
                total_sales: 89500,
                total_commission: 3580,
                sales: [
                    {date: '2024-01-12', invoice: 'SALE-2024-0003', customer: 'خالد أحمد', amount: 12000, commission: 480},
                    {date: '2024-01-18', invoice: 'SALE-2024-0010', customer: 'نورا سالم', amount: 18500, commission: 740},
                    {date: '2024-01-28', invoice: 'SALE-2024-0018', customer: 'حسام محمد', amount: 35000, commission: 1400},
                    {date: '2024-02-03', invoice: 'SALE-2024-0025', customer: 'مريم عبدالله', amount: 24000, commission: 960}
                ]
            },
            3: {
                name: 'سارة أحمد المصري',
                commission_rate: 6,
                total_sales: 67800,
                total_commission: 4068,
                sales: [
                    {date: '2024-01-10', invoice: 'SALE-2024-0002', customer: 'ياسر محمود', amount: 28000, commission: 1680},
                    {date: '2024-01-22', invoice: 'SALE-2024-0012', customer: 'هدى سعد', amount: 15800, commission: 948},
                    {date: '2024-02-02', invoice: 'SALE-2024-0024', customer: 'عمر حسن', amount: 24000, commission: 1440}
                ]
            }
        };

        // عرض تفاصيل العمولة
        function showCommissionDetails(repId) {
            const rep = representativesData[repId];
            if (!rep) return;

            const modalContent = document.getElementById('commissionContent');
            
            modalContent.innerHTML = `
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary">ملخص العمولة</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5>${rep.commission_rate}%</h5>
                                        <small>نسبة العمولة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5>${rep.total_sales.toLocaleString()} جنيه</h5>
                                        <small>إجمالي المبيعات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h5>${rep.total_commission.toLocaleString()} جنيه</h5>
                                        <small>إجمالي العمولة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5>${rep.sales.length}</h5>
                                        <small>عدد الفواتير</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary">تفاصيل المبيعات</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>مبلغ الفاتورة</th>
                                        <th>العمولة</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            rep.sales.forEach(sale => {
                modalContent.innerHTML += `
                    <tr>
                        <td>${new Date(sale.date).toLocaleDateString('ar-EG')}</td>
                        <td><span class="badge bg-primary">${sale.invoice}</span></td>
                        <td>${sale.customer}</td>
                        <td class="text-success fw-bold">${sale.amount.toLocaleString()} جنيه</td>
                        <td class="text-warning fw-bold">${sale.commission.toLocaleString()} جنيه</td>
                    </tr>
                `;
            });

            modalContent.innerHTML += `
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="3">الإجمالي</th>
                                        <th class="text-success">${rep.total_sales.toLocaleString()} جنيه</th>
                                        <th class="text-warning">${rep.total_commission.toLocaleString()} جنيه</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('commissionModal'));
            modal.show();
        }

        // تصدير البيانات
        function exportData() {
            alert('سيتم تصدير بيانات المناديب إلى Excel');
        }
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/admin/representatives/index.blade.php ENDPATH**/ ?>