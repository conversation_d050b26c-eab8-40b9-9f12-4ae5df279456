<?php $__env->startSection('titlePage', 'إنشاء فاتورة'); ?>
<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">إنشاء فاتورة بيع/شراء</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">الفواتير</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-success btn-icon me-2" onclick="printInvoice()">
                    <i class="mdi mdi-printer"></i>
                </button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-info btn-icon me-2" onclick="saveInvoice()">
                    <i class="mdi mdi-content-save"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <div class="row">
        <!-- القسم الرئيسي: بيانات الفاتورة -->
        <div class="col-xl-8">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        بيانات الفاتورة
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="#" id="invoiceForm">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <!-- نوع الفاتورة -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="invoiceType" name="invoice_type" required onchange="toggleCustomerSupplier()">
                                        <option value="">اختر نوع الفاتورة</option>
                                        <option value="sale">فاتورة بيع</option>
                                        <option value="purchase">فاتورة شراء</option>
                                    </select>
                                    <label for="invoiceType">نوع الفاتورة</label>
                                </div>
                            </div>

                            <!-- رقم الفاتورة -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="invoiceNumber" name="invoice_number"
                                        value="INV-<?php echo e(date('Y')); ?>-<?php echo e(str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT)); ?>" readonly>
                                    <label for="invoiceNumber">رقم الفاتورة</label>
                                </div>
                            </div>



                            <!-- العميل/المورد -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="customerSupplier" name="customer_supplier_id" required>
                                        <option value="">اختر العميل/المورد</option>
                                        <!-- سيتم تحديث هذه القائمة بناءً على نوع الفاتورة -->
                                    </select>
                                    <label for="customerSupplier" id="customerSupplierLabel">العميل/المورد</label>
                                </div>
                            </div>

                            <!-- تاريخ الفاتورة -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="invoiceDate" name="invoice_date"
                                        value="<?php echo e(date('Y-m-d')); ?>" required>
                                    <label for="invoiceDate">تاريخ الفاتورة</label>
                                </div>
                            </div>

                            <!-- نوع الدفع -->
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="paymentType" name="payment_type" required onchange="toggleDueDate()">
                                        <option value="">اختر نوع الدفع</option>
                                        <option value="cash">كاش</option>
                                        <option value="credit">أجل</option>
                                    </select>
                                    <label for="paymentType">نوع الدفع</label>
                                </div>
                            </div>

                            <!-- تاريخ الاستحقاق (يظهر فقط عند اختيار أجل) -->
                            <div class="col-md-6" id="dueDateContainer" style="display: none;">
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="dueDate" name="due_date">
                                    <label for="dueDate">تاريخ الاستحقاق</label>
                                </div>
                            </div>

                            <!-- ملاحظات -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="notes" name="notes" style="height: 80px"></textarea>
                                    <label for="notes">ملاحظات</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول المنتجات -->
            <div class="card custom-card mt-3">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        المنتجات
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" onclick="addProductRow()">
                        <i class="mdi mdi-plus"></i> إضافة منتج
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="productsTable">
                            <thead>
                                <tr>
                                    <th width="20%">المخزن</th>
                                    <th width="25%">المنتج</th>
                                    <th width="12%">الكمية</th>
                                    <th width="12%">السعر</th>
                                    <th width="12%">الخصم</th>
                                    <th width="12%">الإجمالي</th>
                                    <th width="7%">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- سيتم إضافة الصفوف هنا ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- القسم الجانبي: ملخص الفاتورة -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">ملخص الفاتورة</div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold">المجموع الفرعي:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="subtotal" class="fs-5">0.00</span> ريال
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold">إجمالي الخصم:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="totalDiscount" class="fs-5 text-success">0.00</span> ريال
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label">الضريبة (15%):</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="taxAmount" class="fs-5">0.00</span> ريال
                        </div>
                    </div>

                    <hr>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold fs-4">الإجمالي النهائي:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="grandTotal" class="fs-4 fw-bold text-primary">0.00</span> ريال
                        </div>
                    </div>

                    <div class="d-grid gap-2 mt-4">
                        <button type="button" class="btn btn-success btn-lg" onclick="saveInvoice()">
                            <i class="mdi mdi-content-save"></i> حفظ الفاتورة
                        </button>
                        <button type="button" class="btn btn-info btn-lg" onclick="printInvoice()">
                            <i class="mdi mdi-printer"></i> طباعة الفاتورة
                        </button>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card custom-card mt-3">
                <div class="card-header">
                    <div class="card-title">معلومات إضافية</div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">حالة الفاتورة:</label>
                        <select class="form-select" id="invoiceStatus" name="status">
                            <option value="draft">مسودة</option>
                            <option value="pending">معلقة</option>
                            <option value="paid">مدفوعة</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المبلغ المدفوع:</label>
                        <input type="number" class="form-control" id="paidAmount" name="paid_amount"
                               step="0.01" min="0" onchange="calculateBalance()">
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">المبلغ المتبقي:</label>
                        <div class="form-control-plaintext fw-bold text-danger" id="remainingAmount">0.00 ريال</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let productRowCounter = 0;

        // تبديل العميل/المورد بناءً على نوع الفاتورة
        function toggleCustomerSupplier() {
            const invoiceType = document.getElementById('invoiceType').value;
            const customerSupplier = document.getElementById('customerSupplier');
            const label = document.getElementById('customerSupplierLabel');

            customerSupplier.innerHTML = '<option value="">اختر...</option>';

            if (invoiceType === 'sale') {
                label.textContent = 'العميل';
                // إضافة العملاء
                customerSupplier.innerHTML += `
                    <option value="1">أحمد محمد علي</option>
                    <option value="2">فاطمة أحمد</option>
                    <option value="3">محمد عبدالله</option>
                `;
            } else if (invoiceType === 'purchase') {
                label.textContent = 'المورد';
                // إضافة الموردين
                customerSupplier.innerHTML += `
                    <option value="1">شركة الأجهزة المتقدمة</option>
                    <option value="2">مؤسسة التقنية الحديثة</option>
                    <option value="3">شركة الإلكترونيات</option>
                `;
            }
        }

        // إظهار/إخفاء تاريخ الاستحقاق
        function toggleDueDate() {
            const paymentType = document.getElementById('paymentType').value;
            const dueDateContainer = document.getElementById('dueDateContainer');

            if (paymentType === 'credit') {
                dueDateContainer.style.display = 'block';
                document.getElementById('dueDate').required = true;
            } else {
                dueDateContainer.style.display = 'none';
                document.getElementById('dueDate').required = false;
            }
        }

        // إضافة صف منتج جديد
        function addProductRow() {
            productRowCounter++;
            const tbody = document.getElementById('productsTableBody');
            const row = document.createElement('tr');
            row.id = `productRow${productRowCounter}`;

            row.innerHTML = `
                <td>
                    <select class="form-select" name="products[${productRowCounter}][warehouse_id]" required onchange="updateProductsByWarehouse(${productRowCounter})">
                        <option value="">اختر المخزن</option>
                        <option value="1">المخزن الرئيسي</option>
                        <option value="2">مخزن الفرع الأول</option>
                        <option value="3">مخزن الفرع الثاني</option>
                    </select>
                </td>
                <td>
                    <select class="form-select" name="products[${productRowCounter}][product_id]" required onchange="updateProductPrice(${productRowCounter})" disabled>
                        <option value="">اختر المخزن أولاً</option>
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control" name="products[${productRowCounter}][quantity]"
                           min="1" value="1" required onchange="calculateRowTotal(${productRowCounter})"
                           title="الكمية المطلوبة">
                    <small class="text-muted" id="stockInfo${productRowCounter}"></small>
                </td>
                <td>
                    <input type="number" class="form-control" name="products[${productRowCounter}][price]"
                           step="0.01" min="0" required onchange="calculateRowTotal(${productRowCounter})">
                </td>
                <td>
                    <input type="number" class="form-control" name="products[${productRowCounter}][discount]"
                           step="0.01" min="0" value="0" onchange="calculateRowTotal(${productRowCounter})">
                </td>
                <td>
                    <span class="fw-bold" id="rowTotal${productRowCounter}">0.00</span>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeProductRow(${productRowCounter})">
                        <i class="mdi mdi-delete"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        }

        // تحديث المنتجات بناءً على المخزن المختار
        function updateProductsByWarehouse(rowId) {
            const warehouseSelect = document.querySelector(`#productRow${rowId} select[name*="[warehouse_id]"]`);
            const productSelect = document.querySelector(`#productRow${rowId} select[name*="[product_id]"]`);
            const warehouseId = warehouseSelect.value;

            // مسح المنتجات الحالية
            productSelect.innerHTML = '<option value="">اختر المنتج</option>';

            if (warehouseId) {
                productSelect.disabled = false;

                // إضافة المنتجات بناءً على المخزن المختار
                let products = [];

                if (warehouseId === '1') { // المخزن الرئيسي
                    products = [
                        {id: '1', name: 'لابتوب HP', price: '2500', stock: '15'},
                        {id: '2', name: 'ماوس لاسلكي', price: '50', stock: '100'},
                        {id: '3', name: 'كيبورد ميكانيكي', price: '150', stock: '50'},
                        {id: '4', name: 'شاشة 24 بوصة', price: '800', stock: '25'},
                        {id: '5', name: 'سماعات بلوتوث', price: '200', stock: '30'}
                    ];
                } else if (warehouseId === '2') { // مخزن الفرع الأول
                    products = [
                        {id: '6', name: 'طابعة ليزر', price: '600', stock: '10'},
                        {id: '7', name: 'كاميرا ويب', price: '120', stock: '20'},
                        {id: '8', name: 'هارد خارجي 1TB', price: '300', stock: '15'},
                        {id: '9', name: 'راوتر واي فاي', price: '180', stock: '12'}
                    ];
                } else if (warehouseId === '3') { // مخزن الفرع الثاني
                    products = [
                        {id: '10', name: 'تابلت سامسونج', price: '1200', stock: '8'},
                        {id: '11', name: 'شاحن لاسلكي', price: '80', stock: '25'},
                        {id: '12', name: 'كابل USB-C', price: '25', stock: '100'},
                        {id: '13', name: 'باور بانك', price: '150', stock: '40'}
                    ];
                }

                // إضافة المنتجات إلى القائمة
                products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.dataset.price = product.price;
                    option.dataset.stock = product.stock;
                    option.textContent = `${product.name} (متوفر: ${product.stock})`;
                    productSelect.appendChild(option);
                });
            } else {
                productSelect.disabled = true;
                productSelect.innerHTML = '<option value="">اختر المخزن أولاً</option>';
            }

            // مسح السعر والإجمالي عند تغيير المخزن
            const priceInput = document.querySelector(`#productRow${rowId} input[name*="[price]"]`);
            priceInput.value = '';
            calculateRowTotal(rowId);
        }

        // تحديث سعر المنتج عند اختياره
        function updateProductPrice(rowId) {
            const productSelect = document.querySelector(`#productRow${rowId} select[name*="[product_id]"]`);
            const priceInput = document.querySelector(`#productRow${rowId} input[name*="[price]"]`);
            const quantityInput = document.querySelector(`#productRow${rowId} input[name*="[quantity]"]`);
            const stockInfo = document.getElementById(`stockInfo${rowId}`);
            const selectedOption = productSelect.options[productSelect.selectedIndex];

            if (selectedOption.dataset.price && selectedOption.dataset.stock) {
                priceInput.value = selectedOption.dataset.price;
                const stock = selectedOption.dataset.stock;
                stockInfo.textContent = `متوفر: ${stock} قطعة`;

                // تحديد الحد الأقصى للكمية
                quantityInput.max = stock;

                // تغيير لون النص حسب الكمية المتاحة
                if (stock < 10) {
                    stockInfo.className = 'text-danger';
                } else if (stock < 20) {
                    stockInfo.className = 'text-warning';
                } else {
                    stockInfo.className = 'text-success';
                }

                calculateRowTotal(rowId);
            } else {
                stockInfo.textContent = '';
                quantityInput.max = '';
            }
        }

        // حساب إجمالي الصف
        function calculateRowTotal(rowId) {
            const row = document.getElementById(`productRow${rowId}`);
            const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
            const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
            const discount = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

            const total = (quantity * price) - discount;
            document.getElementById(`rowTotal${rowId}`).textContent = total.toFixed(2);

            calculateInvoiceTotal();
        }

        // حذف صف المنتج
        function removeProductRow(rowId) {
            document.getElementById(`productRow${rowId}`).remove();
            calculateInvoiceTotal();
        }

        // حساب إجمالي الفاتورة
        function calculateInvoiceTotal() {
            let subtotal = 0;
            let totalDiscount = 0;

            document.querySelectorAll('#productsTableBody tr').forEach(row => {
                const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
                const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
                const discount = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

                subtotal += quantity * price;
                totalDiscount += discount;
            });

            const taxAmount = (subtotal - totalDiscount) * 0.15;
            const grandTotal = subtotal - totalDiscount + taxAmount;

            document.getElementById('subtotal').textContent = subtotal.toFixed(2);
            document.getElementById('totalDiscount').textContent = totalDiscount.toFixed(2);
            document.getElementById('taxAmount').textContent = taxAmount.toFixed(2);
            document.getElementById('grandTotal').textContent = grandTotal.toFixed(2);

            calculateBalance();
        }

        // حساب المبلغ المتبقي
        function calculateBalance() {
            const grandTotal = parseFloat(document.getElementById('grandTotal').textContent) || 0;
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
            const remaining = grandTotal - paidAmount;

            document.getElementById('remainingAmount').textContent = remaining.toFixed(2) + ' ريال';
            document.getElementById('remainingAmount').className = remaining > 0 ?
                'form-control-plaintext fw-bold text-danger' :
                'form-control-plaintext fw-bold text-success';
        }

        // حفظ الفاتورة
        function saveInvoice() {
            const form = document.getElementById('invoiceForm');
            if (form.checkValidity()) {
                alert('تم حفظ الفاتورة بنجاح!');
                // هنا يمكن إضافة كود الحفظ الفعلي
            } else {
                alert('يرجى ملء جميع الحقول المطلوبة');
                form.reportValidity();
            }
        }

        // طباعة الفاتورة
        function printInvoice() {
            window.print();
        }

        // إضافة صف منتج افتراضي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addProductRow();
        });
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/admin/invoice/create.blade.php ENDPATH**/ ?>