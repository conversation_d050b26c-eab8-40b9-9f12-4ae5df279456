<div class="row">
    <div class="col-xl">
        <div class="card custom-card">
            <div class="card-header justify-content-between">
                <div class="card-title">
                    <?php echo e(__('warehouses.add new warehouse')); ?>

                </div>
            </div>
            <div class="card-body">
                <form>
                <div class="row d-flex align-items-center justify-content-center">
                        <?php echo csrf_field(); ?>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="text" wire:model="name" class="form-control" id="floatingInput" placeholder="<?php echo e(__('warehouses.warehouse name')); ?>">
                                <label for="floatingInput"><?php echo e(__('warehouses.warehouse name')); ?></label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" wire:model="address" class="form-control" id="floatingPassword" placeholder="<?php echo e(__('warehouses.warehouse address')); ?>">
                                <label for="floatingAddress"><?php echo e(__('warehouses.warehouse address')); ?></label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button  wire:click.prevent="store" class="btn btn-primary btn-block"><?php echo e(__('buttons.submit warehouse')); ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div><?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/livewire/admin/add-warehouse.blade.php ENDPATH**/ ?>