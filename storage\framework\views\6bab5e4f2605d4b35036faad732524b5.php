<?php $__env->startSection('titlePage', 'العملاء'); ?>
<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">العملاء</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">العملاء</li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <a href="#" class="btn btn-primary">
                    <i class="mdi mdi-plus"></i> إضافة عميل جديد
                </a>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">قائمة العملاء</div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="15%">الاسم</th>
                                    <th width="15%">البريد الإلكتروني</th>
                                    <th width="10%">الهاتف</th>
                                    <th width="10%">المدينة</th>
                                    <th width="12%">له (مدين)</th>
                                    <th width="12%">عليه (دائن)</th>
                                    <th width="10%">آخر استحقاق</th>
                                    <th width="8%">الحالة</th>
                                    <th width="13%">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td class="fw-bold">أحمد محمد علي</td>
                                    <td><EMAIL></td>
                                    <td>0501234567</td>
                                    <td>القاهرة</td>
                                    <td class="text-success fw-bold">2,500.00 جنيه</td>
                                    <td class="text-danger fw-bold">18,500.00 جنيه</td>
                                    <td>
                                        <span class="text-warning">2024-02-15</span>
                                        <br><small class="text-muted">بعد 5 أيام</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">نشط</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                <i class="mdi mdi-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="showFinancialDetails(1)" title="الحساب المالي">
                                                <i class="mdi mdi-cash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td class="fw-bold">فاطمة عبدالله</td>
                                    <td><EMAIL></td>
                                    <td>0509876543</td>
                                    <td>الجيزة</td>
                                    <td class="text-success fw-bold">1,200.00 جنيه</td>
                                    <td class="text-danger fw-bold">19,900.00 جنيه</td>
                                    <td>
                                        <span class="text-danger fw-bold">2024-02-08</span>
                                        <br><small class="text-danger">متأخر يومين</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">معلق</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                <i class="mdi mdi-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="showFinancialDetails(2)" title="الحساب المالي">
                                                <i class="mdi mdi-cash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td class="fw-bold">محمد سعد الغامدي</td>
                                    <td><EMAIL></td>
                                    <td>0551234567</td>
                                    <td>الإسكندرية</td>
                                    <td class="text-success fw-bold">0.00 جنيه</td>
                                    <td class="text-danger fw-bold">25,200.00 جنيه</td>
                                    <td>
                                        <span class="text-success">2024-03-01</span>
                                        <br><small class="text-success">بعد 19 يوم</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">نشط</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="#" class="btn btn-info btn-sm">
                                                <i class="mdi mdi-eye"></i> عرض
                                            </a>
                                            <a href="#" class="btn btn-warning btn-sm">
                                                <i class="mdi mdi-pencil"></i> تعديل
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                                <i class="mdi mdi-delete"></i> حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Modal التفاصيل المالية -->
    <div class="modal fade" id="financialDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">التفاصيل المالية للعميل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="financialDetailsContent">
                    <!-- سيتم عرض التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary">طباعة كشف الحساب</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات العملاء المالية
        const customersFinancialData = {
            1: {
                name: 'أحمد محمد علي',
                debit: 2500,   // له (مرتجعات أو رد زيادة)
                credit: 18500, // عليه (مبيعات مستحقة)
                transactions: [
                    {date: '2024-01-15', type: 'sale', amount: 15000, description: 'فاتورة بيع رقم SALE-2024-0001', due_date: '2024-02-15'},
                    {date: '2024-01-20', type: 'payment', amount: -12000, description: 'دفعة نقدية', due_date: null},
                    {date: '2024-01-25', type: 'sale', amount: 18500, description: 'فاتورة بيع رقم SALE-2024-0015', due_date: '2024-02-25'},
                    {date: '2024-02-01', type: 'return', amount: -2500, description: 'مرتجع بضاعة', due_date: null}
                ]
            },
            2: {
                name: 'فاطمة عبدالله',
                debit: 1200,   // له (مرتجعات)
                credit: 19900, // عليه (مبيعات مستحقة)
                transactions: [
                    {date: '2024-01-10', type: 'sale', amount: 22000, description: 'فاتورة بيع رقم SALE-2024-0005', due_date: '2024-02-08'},
                    {date: '2024-01-18', type: 'payment', amount: -3300, description: 'دفعة نقدية', due_date: null},
                    {date: '2024-01-28', type: 'sale', amount: 2400, description: 'فاتورة بيع رقم SALE-2024-0020', due_date: '2024-02-28'},
                    {date: '2024-02-02', type: 'return', amount: -1200, description: 'مرتجع بضاعة', due_date: null}
                ]
            },
            3: {
                name: 'محمد سعد الغامدي',
                debit: 0,      // له (لا يوجد)
                credit: 25200, // عليه (مبيعات مستحقة)
                transactions: [
                    {date: '2024-01-05', type: 'sale', amount: 30000, description: 'فاتورة بيع رقم SALE-2024-0002', due_date: '2024-03-01'},
                    {date: '2024-01-12', type: 'payment', amount: -4800, description: 'دفعة نقدية', due_date: null},
                    {date: '2024-02-01', type: 'sale', amount: 25200, description: 'فاتورة بيع رقم SALE-2024-0025', due_date: '2024-03-01'}
                ]
            }
        };

        // عرض التفاصيل المالية
        function showFinancialDetails(customerId) {
            const customer = customersFinancialData[customerId];
            if (!customer) return;

            const modalContent = document.getElementById('financialDetailsContent');

            // حساب الرصيد النهائي (عليه - له)
            const balance = customer.credit - customer.debit;
            const balanceClass = balance >= 0 ? 'text-danger' : 'text-success';
            const balanceText = balance >= 0 ? 'عليه' : 'له';

            modalContent.innerHTML = `
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary">ملخص الحساب</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5>${customer.debit.toLocaleString()} جنيه</h5>
                                        <small>إجمالي له (مرتجعات)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h5>${customer.credit.toLocaleString()} جنيه</h5>
                                        <small>إجمالي عليه (مبيعات)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5>${Math.abs(balance).toLocaleString()} جنيه</h5>
                                        <small>الرصيد النهائي (${balanceText})</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h5>${customer.transactions.length}</h5>
                                        <small>عدد المعاملات</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary">كشف الحساب</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>البيان</th>
                                        <th>له</th>
                                        <th>عليه</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            customer.transactions.forEach(transaction => {
                const isDebit = transaction.amount > 0;
                const amount = Math.abs(transaction.amount);
                const typeClass = isDebit ? 'text-success' : 'text-primary';
                const typeIcon = transaction.type === 'sale' ? 'mdi-cart' : 'mdi-cash';

                let statusBadge = '';
                if (transaction.due_date) {
                    const dueDate = new Date(transaction.due_date);
                    const today = new Date();
                    const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

                    if (diffDays < 0) {
                        statusBadge = `<span class="badge bg-danger">متأخر ${Math.abs(diffDays)} يوم</span>`;
                    } else if (diffDays <= 5) {
                        statusBadge = `<span class="badge bg-warning">يستحق خلال ${diffDays} يوم</span>`;
                    } else {
                        statusBadge = `<span class="badge bg-success">يستحق بعد ${diffDays} يوم</span>`;
                    }
                } else {
                    statusBadge = '<span class="badge bg-secondary">مدفوع</span>';
                }

                modalContent.innerHTML += `
                    <tr>
                        <td>${new Date(transaction.date).toLocaleDateString('ar-EG')}</td>
                        <td><i class="mdi ${typeIcon} me-1"></i>${transaction.description}</td>
                        <td class="${isDebit ? 'text-success fw-bold' : ''}">${isDebit ? amount.toLocaleString() + ' جنيه' : '-'}</td>
                        <td class="${!isDebit ? 'text-primary fw-bold' : ''}">${!isDebit ? amount.toLocaleString() + ' جنيه' : '-'}</td>
                        <td>${transaction.due_date ? new Date(transaction.due_date).toLocaleDateString('ar-EG') : '-'}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            });

            modalContent.innerHTML += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('financialDetailsModal'));
            modal.show();
        }
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/admin/customers/index.blade.php ENDPATH**/ ?>