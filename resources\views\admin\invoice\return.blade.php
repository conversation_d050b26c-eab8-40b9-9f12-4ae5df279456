@extends('admin.layouts.master')
@section('titlePage', 'فاتورة مرتجع')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">فاتورة مرتجع</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active" aria-current="page">فاتورة مرتجع</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-success btn-icon me-2" onclick="printInvoice()">
                    <i class="mdi mdi-printer"></i>
                </button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-info btn-icon me-2" onclick="saveInvoice()">
                    <i class="mdi mdi-content-save"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <div class="row">
        <!-- القسم الرئيسي: بيانات الفاتورة والمنتجات -->
        <div class="col-xl-8">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        بيانات فاتورة المرتجع
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="#" id="returnInvoiceForm">
                        @csrf
                        <div class="row">
                            <!-- نوع المرتجع -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="returnType" name="return_type" required onchange="toggleReturnType()">
                                        <option value="">اختر نوع المرتجع</option>
                                        <option value="sale_return">مرتجع مبيعات</option>
                                        <option value="purchase_return">مرتجع مشتريات</option>
                                    </select>
                                    <label for="returnType">نوع المرتجع</label>
                                </div>
                            </div>

                            <!-- رقم الفاتورة -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="returnNumber" name="return_number"
                                        value="RET-{{ date('Y') }}-{{ str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT) }}" readonly>
                                    <label for="returnNumber">رقم فاتورة المرتجع</label>
                                </div>
                            </div>

                            <!-- تاريخ المرتجع -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="returnDate" name="return_date"
                                        value="{{ date('Y-m-d') }}" required>
                                    <label for="returnDate">تاريخ المرتجع</label>
                                </div>
                            </div>

                            <!-- الفاتورة الأصلية -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="originalInvoice" name="original_invoice_id" required>
                                        <option value="">اختر الفاتورة الأصلية</option>
                                        <!-- سيتم تحديث هذه القائمة بناءً على نوع المرتجع -->
                                    </select>
                                    <label for="originalInvoice">الفاتورة الأصلية</label>
                                </div>
                            </div>

                            <!-- العميل/المورد -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="customerSupplier" name="customer_supplier_id" required>
                                        <option value="">اختر العميل/المورد</option>
                                    </select>
                                    <label for="customerSupplier" id="customerSupplierLabel">العميل/المورد</label>
                                </div>
                            </div>

                            <!-- سبب المرتجع -->
                            <div class="col-md-4">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="returnReason" name="return_reason" required>
                                        <option value="">اختر سبب المرتجع</option>
                                        <option value="defective">عيب في المنتج</option>
                                        <option value="wrong_item">منتج خاطئ</option>
                                        <option value="damaged">تلف أثناء الشحن</option>
                                        <option value="customer_request">طلب العميل</option>
                                        <option value="expired">منتهي الصلاحية</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                    <label for="returnReason">سبب المرتجع</label>
                                </div>
                            </div>

                            <!-- ملاحظات -->
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="notes" name="notes" style="height: 60px"></textarea>
                                    <label for="notes">ملاحظات</label>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- جدول المنتجات المرتجعة -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">المنتجات المرتجعة</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="addReturnRow()">
                                <i class="mdi mdi-plus"></i> إضافة منتج
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered" id="returnTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="20%">المخزن</th>
                                        <th width="25%">المنتج</th>
                                        <th width="12%">الكمية المرتجعة</th>
                                        <th width="15%">السعر</th>
                                        <th width="12%">الخصم</th>
                                        <th width="13%">الإجمالي</th>
                                        <th width="3%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody id="returnTableBody">
                                    <!-- سيتم إضافة الصفوف هنا ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- القسم الجانبي: ملخص المرتجع -->
        <div class="col-xl-4">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">ملخص فاتورة المرتجع</div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold">المجموع الفرعي:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="subtotal" class="fs-5">0.00</span> جنيه
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold">إجمالي الخصم:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="totalDiscount" class="fs-5 text-success">0.00</span> جنيه
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label">الضريبة (14%):</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="taxAmount" class="fs-5">0.00</span> جنيه
                        </div>
                    </div>

                    <hr>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label fw-bold fs-4">إجمالي المرتجع:</label>
                        </div>
                        <div class="col-6 text-end">
                            <span id="grandTotal" class="fs-4 fw-bold text-danger">0.00</span> جنيه
                        </div>
                    </div>

                    <hr>

                    <!-- معلومات الإرجاع -->
                    <h6 class="mb-3">معلومات الإرجاع</h6>

                    <div class="row mb-3">
                        <div class="col-6">
                            <label class="form-label">حالة المرتجع:</label>
                            <select class="form-select" id="returnStatus" name="status">
                                <option value="pending">معلق</option>
                                <option value="approved">موافق عليه</option>
                                <option value="rejected">مرفوض</option>
                                <option value="processed">تم المعالجة</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label">المبلغ المسترد:</label>
                            <input type="number" class="form-control" id="refundAmount" name="refund_amount"
                                   step="0.01" min="0" onchange="calculateRefundBalance()">
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-6">
                            <label class="form-label fw-bold">المبلغ المتبقي:</label>
                        </div>
                        <div class="col-6 text-end">
                            <div class="fw-bold text-warning fs-5" id="remainingRefund">0.00 جنيه</div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-lg" onclick="saveInvoice()">
                            <i class="mdi mdi-content-save"></i> حفظ المرتجع
                        </button>
                        <button type="button" class="btn btn-info btn-lg" onclick="printInvoice()">
                            <i class="mdi mdi-printer"></i> طباعة المرتجع
                        </button>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card custom-card mt-3">
                <div class="card-header">
                    <div class="card-title">معلومات إضافية</div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">تنبيه:</h6>
                        <p class="mb-0">سيتم إضافة المنتجات المرتجعة إلى المخزون تلقائياً عند الموافقة على المرتجع.</p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">تاريخ الموافقة:</label>
                        <input type="date" class="form-control" id="approvalDate" name="approval_date">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الموافق:</label>
                        <input type="text" class="form-control" id="approvedBy" name="approved_by"
                               placeholder="اسم الموظف المسؤول">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let returnRowCounter = 0;

        // تبديل نوع المرتجع
        function toggleReturnType() {
            const returnType = document.getElementById('returnType').value;
            const customerSupplier = document.getElementById('customerSupplier');
            const label = document.getElementById('customerSupplierLabel');
            const originalInvoice = document.getElementById('originalInvoice');

            // مسح القوائم
            customerSupplier.innerHTML = '<option value="">اختر...</option>';
            originalInvoice.innerHTML = '<option value="">اختر الفاتورة الأصلية</option>';

            if (returnType === 'sale_return') {
                label.textContent = 'العميل';
                // إضافة العملاء
                customerSupplier.innerHTML += `
                    <option value="1">أحمد محمد علي</option>
                    <option value="2">فاطمة أحمد</option>
                    <option value="3">محمد عبدالله</option>
                `;
                // إضافة فواتير البيع
                originalInvoice.innerHTML += `
                    <option value="SALE-2024-0001">SALE-2024-0001 - أحمد محمد علي</option>
                    <option value="SALE-2024-0005">SALE-2024-0005 - فاطمة أحمد</option>
                    <option value="SALE-2024-0010">SALE-2024-0010 - محمد عبدالله</option>
                `;
            } else if (returnType === 'purchase_return') {
                label.textContent = 'المورد';
                // إضافة الموردين
                customerSupplier.innerHTML += `
                    <option value="1">شركة الأجهزة المتقدمة</option>
                    <option value="2">مؤسسة التقنية الحديثة</option>
                    <option value="3">شركة الإلكترونيات المصرية</option>
                `;
                // إضافة فواتير الشراء
                originalInvoice.innerHTML += `
                    <option value="PUR-2024-0001">PUR-2024-0001 - شركة الأجهزة المتقدمة</option>
                    <option value="PUR-2024-0003">PUR-2024-0003 - مؤسسة التقنية الحديثة</option>
                    <option value="PUR-2024-0007">PUR-2024-0007 - شركة الإلكترونيات المصرية</option>
                `;
            }
        }

        // إضافة صف منتج مرتجع
        function addReturnRow() {
            returnRowCounter++;
            const tbody = document.getElementById('returnTableBody');
            const row = document.createElement('tr');
            row.id = `returnRow${returnRowCounter}`;

            row.innerHTML = `
                <td>
                    <select class="form-select form-select-sm" name="returns[${returnRowCounter}][warehouse_id]" required>
                        <option value="">اختر المخزن</option>
                        <option value="1">المخزن الرئيسي</option>
                        <option value="2">مخزن الفرع الأول</option>
                        <option value="3">مخزن الفرع الثاني</option>
                    </select>
                </td>
                <td>
                    <select class="form-select form-select-sm" name="returns[${returnRowCounter}][product_id]" required onchange="updateReturnPrice(${returnRowCounter})">
                        <option value="">اختر المنتج</option>
                        <option value="1" data-price="15000">لابتوب HP</option>
                        <option value="2" data-price="250">ماوس لاسلكي</option>
                        <option value="3" data-price="750">كيبورد ميكانيكي</option>
                        <option value="4" data-price="4000">شاشة 24 بوصة</option>
                        <option value="5" data-price="1000">سماعات بلوتوث</option>
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" name="returns[${returnRowCounter}][quantity]"
                           min="1" value="1" required onchange="calculateReturnTotal(${returnRowCounter})">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" name="returns[${returnRowCounter}][price]"
                           step="0.01" min="0" required onchange="calculateReturnTotal(${returnRowCounter})">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" name="returns[${returnRowCounter}][discount]"
                           step="0.01" min="0" value="0" onchange="calculateReturnTotal(${returnRowCounter})">
                </td>
                <td>
                    <span class="fw-bold text-danger" id="returnTotal${returnRowCounter}">0.00</span>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeReturnRow(${returnRowCounter})">
                        <i class="mdi mdi-delete"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        }

        // تحديث سعر المنتج المرتجع
        function updateReturnPrice(rowId) {
            const productSelect = document.querySelector(`#returnRow${rowId} select[name*="[product_id]"]`);
            const priceInput = document.querySelector(`#returnRow${rowId} input[name*="[price]"]`);
            const selectedOption = productSelect.options[productSelect.selectedIndex];

            if (selectedOption.dataset.price) {
                priceInput.value = selectedOption.dataset.price;
                calculateReturnTotal(rowId);
            }
        }

        // حساب إجمالي صف المرتجع
        function calculateReturnTotal(rowId) {
            const row = document.getElementById(`returnRow${rowId}`);
            const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
            const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
            const discount = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

            const total = (quantity * price) - discount;
            document.getElementById(`returnTotal${rowId}`).textContent = total.toFixed(2);

            calculateReturnInvoiceTotal();
        }

        // حذف صف المرتجع
        function removeReturnRow(rowId) {
            document.getElementById(`returnRow${rowId}`).remove();
            calculateReturnInvoiceTotal();
        }

        // حساب إجمالي فاتورة المرتجع
        function calculateReturnInvoiceTotal() {
            let subtotal = 0;
            let totalDiscount = 0;

            document.querySelectorAll('#returnTableBody tr').forEach(row => {
                const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
                const price = parseFloat(row.querySelector('input[name*="[price]"]').value) || 0;
                const discount = parseFloat(row.querySelector('input[name*="[discount]"]').value) || 0;

                subtotal += quantity * price;
                totalDiscount += discount;
            });

            const taxAmount = (subtotal - totalDiscount) * 0.14;
            const grandTotal = subtotal - totalDiscount + taxAmount;

            document.getElementById('subtotal').textContent = subtotal.toFixed(2);
            document.getElementById('totalDiscount').textContent = totalDiscount.toFixed(2);
            document.getElementById('taxAmount').textContent = taxAmount.toFixed(2);
            document.getElementById('grandTotal').textContent = grandTotal.toFixed(2);

            calculateRefundBalance();
        }

        // حساب رصيد الاسترداد
        function calculateRefundBalance() {
            const grandTotal = parseFloat(document.getElementById('grandTotal').textContent) || 0;
            const refundAmount = parseFloat(document.getElementById('refundAmount').value) || 0;
            const remaining = grandTotal - refundAmount;

            document.getElementById('remainingRefund').textContent = remaining.toFixed(2) + ' جنيه';
            document.getElementById('remainingRefund').className = remaining > 0 ?
                'fw-bold text-warning fs-5' :
                'fw-bold text-success fs-5';
        }

        // حفظ فاتورة المرتجع
        function saveInvoice() {
            const form = document.getElementById('returnInvoiceForm');
            if (form.checkValidity()) {
                alert('تم حفظ فاتورة المرتجع بنجاح!');
            } else {
                alert('يرجى ملء جميع الحقول المطلوبة');
                form.reportValidity();
            }
        }

        // طباعة فاتورة المرتجع
        function printInvoice() {
            window.print();
        }

        // إضافة صف مرتجع افتراضي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addReturnRow();
        });
    </script>

@endsection
