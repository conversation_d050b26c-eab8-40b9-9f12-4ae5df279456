@extends('admin.layouts.master')
@section('titlePage', 'البحث عن المنتجات')
@section('content')
    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">البحث عن المنتجات في المخازن</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="javascript:void(0);">المنتجات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">البحث</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-success btn-icon me-2" onclick="exportResults()">
                    <i class="mdi mdi-file-excel"></i>
                </button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-info btn-icon me-2" onclick="printResults()">
                    <i class="mdi mdi-printer"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- Page Header Close -->

    <div class="row">
        <!-- قسم البحث -->
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="mdi mdi-magnify me-2"></i>البحث عن المنتجات
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- البحث بالاسم -->
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="productName" placeholder="اسم المنتج">
                                <label for="productName">اسم المنتج</label>
                            </div>
                        </div>

                        <!-- البحث بالمخزن -->
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="warehouseFilter">
                                    <option value="">جميع المخازن</option>
                                    <option value="1">المخزن الرئيسي</option>
                                    <option value="2">مخزن الفرع الأول</option>
                                    <option value="3">مخزن الفرع الثاني</option>
                                </select>
                                <label for="warehouseFilter">المخزن</label>
                            </div>
                        </div>

                        <!-- البحث بالمورد -->
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="supplierFilter">
                                    <option value="">جميع الموردين</option>
                                    <option value="1">شركة الأجهزة المتقدمة</option>
                                    <option value="2">مؤسسة التقنية الحديثة</option>
                                    <option value="3">شركة الإلكترونيات المصرية</option>
                                </select>
                                <label for="supplierFilter">المورد</label>
                            </div>
                        </div>

                        <!-- حالة المخزون -->
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="stockStatus">
                                    <option value="">جميع الحالات</option>
                                    <option value="available">متوفر</option>
                                    <option value="low">مخزون منخفض</option>
                                    <option value="out">نفد المخزون</option>
                                </select>
                                <label for="stockStatus">حالة المخزون</label>
                            </div>
                        </div>

                        <!-- نطاق السعر من -->
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="priceFrom" step="0.01" min="0">
                                <label for="priceFrom">السعر من (جنيه)</label>
                            </div>
                        </div>

                        <!-- نطاق السعر إلى -->
                        <div class="col-md-4">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="priceTo" step="0.01" min="0">
                                <label for="priceTo">السعر إلى (جنيه)</label>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" onclick="searchProducts()">
                            <i class="mdi mdi-magnify"></i> بحث
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearSearch()">
                            <i class="mdi mdi-refresh"></i> مسح
                        </button>
                        <button type="button" class="btn btn-info" onclick="showAllProducts()">
                            <i class="mdi mdi-view-list"></i> عرض الكل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نتائج البحث -->
    <div class="row mt-3">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="card-title">
                        <i class="mdi mdi-format-list-bulleted me-2"></i>نتائج البحث
                    </div>
                    <div>
                        <span class="badge bg-primary" id="resultsCount">0 منتج</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="productsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th width="15%">اسم المنتج</th>
                                    <th width="12%">المخزن</th>
                                    <th width="8%">الكمية</th>
                                    <th width="10%">سعر البيع</th>
                                    <th width="10%">سعر الشراء</th>
                                    <th width="12%">المورد</th>
                                    <th width="10%">آخر شراء</th>
                                    <th width="8%">تاريخ الشراء</th>
                                    <th width="8%">الحالة</th>
                                    <th width="7%">تفاصيل</th>
                                </tr>
                            </thead>
                            <tbody id="productsTableBody">
                                <!-- سيتم عرض النتائج هنا -->
                            </tbody>
                        </table>
                    </div>

                    <!-- رسالة عدم وجود نتائج -->
                    <div id="noResults" class="text-center py-5" style="display: none;">
                        <i class="mdi mdi-magnify fs-1 text-muted"></i>
                        <h5 class="text-muted mt-3">لا توجد نتائج</h5>
                        <p class="text-muted">جرب تغيير معايير البحث</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل المنتج -->
    <div class="modal fade" id="productDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل المنتج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="productDetailsContent">
                    <!-- سيتم عرض التفاصيل هنا -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المنتجات التجريبية
        const productsData = [
            {
                id: 1,
                name: 'لابتوب HP',
                warehouse: 'المخزن الرئيسي',
                warehouse_id: 1,
                stock: 15,
                sale_price: 15000,
                purchase_price: 12000,
                supplier: 'شركة الأجهزة المتقدمة',
                supplier_id: 1,
                last_purchase_price: 12000,
                last_purchase_date: '2024-01-15',
                status: 'available',
                min_stock: 5,
                category: 'أجهزة كمبيوتر'
            },
            {
                id: 2,
                name: 'ماوس لاسلكي',
                warehouse: 'المخزن الرئيسي',
                warehouse_id: 1,
                stock: 100,
                sale_price: 250,
                purchase_price: 200,
                supplier: 'مؤسسة التقنية الحديثة',
                supplier_id: 2,
                last_purchase_price: 200,
                last_purchase_date: '2024-01-20',
                status: 'available',
                min_stock: 20,
                category: 'ملحقات'
            },
            {
                id: 3,
                name: 'كيبورد ميكانيكي',
                warehouse: 'المخزن الرئيسي',
                warehouse_id: 1,
                stock: 8,
                sale_price: 750,
                purchase_price: 600,
                supplier: 'شركة الإلكترونيات المصرية',
                supplier_id: 3,
                last_purchase_price: 580,
                last_purchase_date: '2024-01-10',
                status: 'low',
                min_stock: 10,
                category: 'ملحقات'
            },
            {
                id: 4,
                name: 'شاشة 24 بوصة',
                warehouse: 'مخزن الفرع الأول',
                warehouse_id: 2,
                stock: 25,
                sale_price: 4000,
                purchase_price: 3200,
                supplier: 'شركة الأجهزة المتقدمة',
                supplier_id: 1,
                last_purchase_price: 3200,
                last_purchase_date: '2024-01-18',
                status: 'available',
                min_stock: 5,
                category: 'شاشات'
            },
            {
                id: 5,
                name: 'سماعات بلوتوث',
                warehouse: 'مخزن الفرع الثاني',
                warehouse_id: 3,
                stock: 0,
                sale_price: 1000,
                purchase_price: 800,
                supplier: 'مؤسسة التقنية الحديثة',
                supplier_id: 2,
                last_purchase_price: 800,
                last_purchase_date: '2024-01-05',
                status: 'out',
                min_stock: 10,
                category: 'صوتيات'
            }
        ];

        let filteredProducts = [...productsData];

        // البحث في المنتجات
        function searchProducts() {
            const productName = document.getElementById('productName').value.toLowerCase();
            const warehouseFilter = document.getElementById('warehouseFilter').value;
            const supplierFilter = document.getElementById('supplierFilter').value;
            const stockStatus = document.getElementById('stockStatus').value;
            const priceFrom = parseFloat(document.getElementById('priceFrom').value) || 0;
            const priceTo = parseFloat(document.getElementById('priceTo').value) || Infinity;

            filteredProducts = productsData.filter(product => {
                const nameMatch = !productName || product.name.toLowerCase().includes(productName);
                const warehouseMatch = !warehouseFilter || product.warehouse_id == warehouseFilter;
                const supplierMatch = !supplierFilter || product.supplier_id == supplierFilter;
                const priceMatch = product.sale_price >= priceFrom && product.sale_price <= priceTo;

                let statusMatch = true;
                if (stockStatus === 'available') statusMatch = product.stock > product.min_stock;
                else if (stockStatus === 'low') statusMatch = product.stock > 0 && product.stock <= product.min_stock;
                else if (stockStatus === 'out') statusMatch = product.stock === 0;

                return nameMatch && warehouseMatch && supplierMatch && priceMatch && statusMatch;
            });

            displayResults();
        }

        // عرض النتائج
        function displayResults() {
            const tbody = document.getElementById('productsTableBody');
            const noResults = document.getElementById('noResults');
            const resultsCount = document.getElementById('resultsCount');

            tbody.innerHTML = '';

            if (filteredProducts.length === 0) {
                noResults.style.display = 'block';
                resultsCount.textContent = '0 منتج';
                return;
            }

            noResults.style.display = 'none';
            resultsCount.textContent = `${filteredProducts.length} منتج`;

            filteredProducts.forEach(product => {
                const row = document.createElement('tr');

                // تحديد لون الحالة
                let statusBadge = '';
                let stockClass = '';

                if (product.stock === 0) {
                    statusBadge = '<span class="badge bg-danger">نفد المخزون</span>';
                    stockClass = 'text-danger fw-bold';
                } else if (product.stock <= product.min_stock) {
                    statusBadge = '<span class="badge bg-warning">مخزون منخفض</span>';
                    stockClass = 'text-warning fw-bold';
                } else {
                    statusBadge = '<span class="badge bg-success">متوفر</span>';
                    stockClass = 'text-success fw-bold';
                }

                row.innerHTML = `
                    <td class="fw-bold">${product.name}</td>
                    <td>${product.warehouse}</td>
                    <td class="${stockClass}">${product.stock}</td>
                    <td>${product.sale_price.toLocaleString()} جنيه</td>
                    <td>${product.purchase_price.toLocaleString()} جنيه</td>
                    <td>${product.supplier}</td>
                    <td>${product.last_purchase_price.toLocaleString()} جنيه</td>
                    <td>${new Date(product.last_purchase_date).toLocaleDateString('ar-EG')}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-info btn-sm" onclick="showProductDetails(${product.id})">
                            <i class="mdi mdi-eye"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // عرض تفاصيل المنتج
        function showProductDetails(productId) {
            const product = productsData.find(p => p.id === productId);
            if (!product) return;

            const modalContent = document.getElementById('productDetailsContent');

            modalContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات أساسية</h6>
                        <table class="table table-sm">
                            <tr><td class="fw-bold">اسم المنتج:</td><td>${product.name}</td></tr>
                            <tr><td class="fw-bold">التصنيف:</td><td>${product.category}</td></tr>
                            <tr><td class="fw-bold">المخزن:</td><td>${product.warehouse}</td></tr>
                            <tr><td class="fw-bold">المورد:</td><td>${product.supplier}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">معلومات المخزون</h6>
                        <table class="table table-sm">
                            <tr><td class="fw-bold">الكمية الحالية:</td><td class="${product.stock <= product.min_stock ? 'text-danger' : 'text-success'} fw-bold">${product.stock}</td></tr>
                            <tr><td class="fw-bold">الحد الأدنى:</td><td>${product.min_stock}</td></tr>
                            <tr><td class="fw-bold">سعر البيع:</td><td class="text-success fw-bold">${product.sale_price.toLocaleString()} جنيه</td></tr>
                            <tr><td class="fw-bold">سعر الشراء:</td><td>${product.purchase_price.toLocaleString()} جنيه</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-primary">آخر عملية شراء</h6>
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>آخر سعر شراء:</strong><br>
                                    <span class="fs-5 text-primary">${product.last_purchase_price.toLocaleString()} جنيه</span>
                                </div>
                                <div class="col-md-4">
                                    <strong>تاريخ آخر شراء:</strong><br>
                                    <span class="fs-6">${new Date(product.last_purchase_date).toLocaleDateString('ar-EG')}</span>
                                </div>
                                <div class="col-md-4">
                                    <strong>الفرق في السعر:</strong><br>
                                    <span class="fs-6 ${product.last_purchase_price > product.purchase_price ? 'text-danger' : 'text-success'}">
                                        ${product.last_purchase_price > product.purchase_price ? '+' : ''}${(product.last_purchase_price - product.purchase_price).toLocaleString()} جنيه
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary">إحصائيات</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="text-success">${((product.sale_price - product.purchase_price) / product.purchase_price * 100).toFixed(1)}%</h5>
                                        <small>هامش الربح</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="text-info">${(product.sale_price * product.stock).toLocaleString()}</h5>
                                        <small>قيمة المخزون</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="text-warning">${Math.ceil(product.stock / 30)}</h5>
                                        <small>شهور التغطية</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="text-primary">${product.min_stock - product.stock > 0 ? product.min_stock - product.stock : 0}</h5>
                                        <small>كمية مطلوبة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const modal = new bootstrap.Modal(document.getElementById('productDetailsModal'));
            modal.show();
        }

        // مسح البحث
        function clearSearch() {
            document.getElementById('productName').value = '';
            document.getElementById('warehouseFilter').value = '';
            document.getElementById('supplierFilter').value = '';
            document.getElementById('stockStatus').value = '';
            document.getElementById('priceFrom').value = '';
            document.getElementById('priceTo').value = '';

            filteredProducts = [...productsData];
            displayResults();
        }

        // عرض جميع المنتجات
        function showAllProducts() {
            clearSearch();
        }

        // تصدير النتائج
        function exportResults() {
            alert('سيتم تصدير النتائج إلى Excel');
            // هنا يمكن إضافة كود التصدير الفعلي
        }

        // طباعة النتائج
        function printResults() {
            window.print();
        }

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            showAllProducts();
        });
    </script>

@endsection
